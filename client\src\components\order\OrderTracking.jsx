import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import config from '../../config/environment.js';
import {
  registerAsUser,
  listenForOrderAccepted,
  listenForLocationUpdates,
  listenForOrderStatusUpdates
} from '../../services/socketService';
import LeafletMap from '../common/LeafletMap';

// API base URL
const API_BASE_URL = config.API_BASE_URL;

const OrderTracking = () => {
  const { orderId } = useParams();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deliveryPersonLocation, setDeliveryPersonLocation] = useState(null);
  const [estimatedArrival, setEstimatedArrival] = useState({ minutes: 15, text: '15 mins', onTime: true });
  const [customerLocation, setCustomerLocation] = useState(null);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [isLocationUpdating, setIsLocationUpdating] = useState(false);

  // Fetch order details
  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);

        const response = await axios.get(`${API_BASE_URL}/order-api/order/${orderId}`);

        if (response.data && response.data.payload) {
          const orderData = response.data.payload;
          setOrder(orderData);

          // Set customer location if available
          if (orderData.deliveryAddress && orderData.deliveryAddress.coordinates) {
            setCustomerLocation({
              lat: orderData.deliveryAddress.coordinates.latitude,
              lng: orderData.deliveryAddress.coordinates.longitude
            });
          }

          // Set delivery person location if available
          if (orderData.deliveryPersonLocation) {
            setDeliveryPersonLocation({
              lat: orderData.deliveryPersonLocation.lat,
              lng: orderData.deliveryPersonLocation.lng
            });
          }
        } else {
          setError('Failed to fetch order details');
        }
      } catch (error) {
        console.error('Error fetching order:', error);
        setError('Failed to fetch order details');
        toast.error('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [orderId]);

  // Register for real-time updates
  useEffect(() => {
    if (order && order.userId) {
      // Register as a user to receive updates
      registerAsUser(order.userId);

      // Listen for order accepted events
      const cleanupAccepted = listenForOrderAccepted((data) => {
        if (data.orderId === orderId) {
          // Update the order with delivery person details
          setOrder(prevOrder => ({
            ...prevOrder,
            deliveryPersonDetails: data.deliveryPerson,
            expectedDeliveryTime: data.expectedDeliveryTime,
            orderStatus: 'OUT_FOR_DELIVERY'
          }));

          toast.success('Your order has been accepted by a delivery partner!');
        }
      });

      return cleanupAccepted;
    }
  }, [order, orderId]);

  // Listen for location updates
  useEffect(() => {
    if (order && order.orderStatus === 'OUT_FOR_DELIVERY') {
      // Listen for location updates
      const cleanupLocation = listenForLocationUpdates(orderId, (data) => {
        console.log('Location update received:', data);

        // Show visual indicator that location is updating
        setIsLocationUpdating(true);

        // Update delivery person location
        if (data.location) {
          setDeliveryPersonLocation({
            lat: data.location.lat,
            lng: data.location.lng
          });

          // Update last update time
          setLastUpdateTime(new Date());

          // Reset the visual indicator after 1 second
          setTimeout(() => {
            setIsLocationUpdating(false);
          }, 1000);
        }

        // Update estimated arrival time
        if (data.estimatedArrival) {
          setEstimatedArrival(data.estimatedArrival);
        }
      });

      return cleanupLocation;
    }
  }, [order, orderId]);

  // Listen for order status updates
  useEffect(() => {
    if (order) {
      // Listen for order status updates
      const cleanupStatus = listenForOrderStatusUpdates(orderId, (data) => {
        console.log('Order status update received:', data);

        // Update the order status
        setOrder(prevOrder => ({
          ...prevOrder,
          orderStatus: data.status
        }));

        // If the order is delivered, clear delivery person location
        if (data.status === 'DELIVERED') {
          toast.success('Your order has been delivered!');

          // Clear delivery person location to stop showing it on the map
          setDeliveryPersonLocation(null);

          // Update last update time to show when delivery was completed
          setLastUpdateTime(new Date());
        }
      });

      return cleanupStatus;
    }
  }, [order, orderId]);

  // Format last update time
  const getLastUpdateText = () => {
    if (!lastUpdateTime) return 'Waiting for updates...';

    const now = new Date();
    const diff = Math.floor((now - lastUpdateTime) / 1000); // difference in seconds

    if (diff < 60) {
      return `Updated ${diff} seconds ago`;
    } else if (diff < 3600) {
      const minutes = Math.floor(diff / 60);
      return `Updated ${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return `Updated at ${lastUpdateTime.toLocaleTimeString()}`;
    }
  };

  // Get status step
  const getStatusStep = (status) => {
    switch (status) {
      case 'PLACED':
        return 1;
      case 'CONFIRMED':
        return 2;
      case 'PREPARING':
        return 3;
      case 'OUT_FOR_DELIVERY':
        return 4;
      case 'DELIVERED':
        return 5;
      default:
        return 1;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format expected delivery time
  const formatExpectedDelivery = (dateString) => {
    if (!dateString) return 'Not available';

    const options = {
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleTimeString(undefined, options);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 mt-16 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8a4af3]"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="container mx-auto px-4 py-12 mt-16">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg className="h-16 w-16 text-red-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Order Not Found</h2>
          <p className="text-gray-600 mb-6">{error || 'The order you are looking for does not exist or has been removed.'}</p>
          <Link to="/" className="px-6 py-3 bg-[#8a4af3] text-white rounded-md hover:bg-[#8a4af3]/90 transition-colors">
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  const statusStep = getStatusStep(order.orderStatus);

  return (
    <div className="container mx-auto px-4 py-12 mt-16 font-['Gilroy',arial,'Helvetica Neue',sans-serif]">
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Header */}
        <div className="bg-[#8a4af3] text-white p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-2xl font-bold">Order Tracking</h1>
              <p className="text-white/80">Order ID: {order._id}</p>
            </div>
            <div className="mt-4 md:mt-0">
              <span className={`px-4 py-2 rounded-full text-sm font-medium ${
                order.orderStatus === 'DELIVERED' ? 'bg-green-500' :
                order.orderStatus === 'CANCELLED' ? 'bg-red-500' :
                'bg-white/20'
              }`}>
                {order.orderStatus}
              </span>
            </div>
          </div>
        </div>

        {/* Order Progress */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-6">Order Progress</h2>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="h-1 w-full bg-gray-200 rounded"></div>
            </div>
            <div className="relative flex justify-between">
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full ${statusStep >= 1 ? 'bg-[#8a4af3]' : 'bg-gray-200'} flex items-center justify-center`}>
                  {statusStep > 1 ? (
                    <svg className="w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-white font-medium">1</span>
                  )}
                </div>
                <span className="text-sm font-medium mt-2">Placed</span>
              </div>
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full ${statusStep >= 2 ? 'bg-[#8a4af3]' : 'bg-gray-200'} flex items-center justify-center`}>
                  {statusStep > 2 ? (
                    <svg className="w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-white font-medium">2</span>
                  )}
                </div>
                <span className="text-sm font-medium mt-2">Confirmed</span>
              </div>
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full ${statusStep >= 3 ? 'bg-[#8a4af3]' : 'bg-gray-200'} flex items-center justify-center`}>
                  {statusStep > 3 ? (
                    <svg className="w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-white font-medium">3</span>
                  )}
                </div>
                <span className="text-sm font-medium mt-2">Preparing</span>
              </div>
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full ${statusStep >= 4 ? 'bg-[#8a4af3]' : 'bg-gray-200'} flex items-center justify-center`}>
                  {statusStep > 4 ? (
                    <svg className="w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-white font-medium">4</span>
                  )}
                </div>
                <span className="text-sm font-medium mt-2">Out for Delivery</span>
              </div>
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full ${statusStep >= 5 ? 'bg-[#8a4af3]' : 'bg-gray-200'} flex items-center justify-center`}>
                  {statusStep > 5 ? (
                    <svg className="w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-white font-medium">5</span>
                  )}
                </div>
                <span className="text-sm font-medium mt-2">Delivered</span>
              </div>
            </div>
          </div>
        </div>

        {/* Live Tracking Map */}
        <div className="border-b border-gray-200">
          <div className="h-96 relative">
            {order.orderStatus === 'OUT_FOR_DELIVERY' && deliveryPersonLocation && customerLocation ? (
              <LeafletMap
                deliveryPersonLocation={deliveryPersonLocation}
                customerLocation={customerLocation}
                showDirections={true}
                deliveryPersonName={order.deliveryPersonDetails?.name || 'Delivery Partner'}
                estimatedTime={estimatedArrival.text}
                onTimeStatus={estimatedArrival.onTime}
                lastUpdateText={getLastUpdateText()}
                isLocationUpdating={isLocationUpdating}
              />
            ) : order.orderStatus === 'DELIVERED' && customerLocation ? (
              <LeafletMap
                customerLocation={customerLocation}
                showDirections={false}
                estimatedTime="0 mins"
                onTimeStatus={true}
                lastUpdateText="Order delivered"
              />
            ) : customerLocation ? (
              <div className="relative h-full">
                <LeafletMap
                  customerLocation={customerLocation}
                  showDirections={false}
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                  <div className="bg-white p-6 rounded-lg shadow-lg max-w-md text-center">
                    <svg className="h-16 w-16 text-[#8a4af3] mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900">
                      {order.orderStatus === 'CONFIRMED' ? 'Order Confirmed' : 'Preparing Your Order'}
                    </h3>
                    <p className="text-gray-600 mt-2">
                      Your order has been confirmed and is being prepared. A delivery partner will be assigned soon.
                    </p>
                    <p className="mt-4 text-sm text-[#8a4af3]">
                      You'll see the delivery partner's location here once they're on the way.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-100">
                <div className="text-center p-6">
                  <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <p className="text-gray-600">Waiting for delivery location information...</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Delivery Person Details */}
        {order.deliveryPersonDetails && order.orderStatus === 'OUT_FOR_DELIVERY' && (
          <div className="p-6 border-b border-gray-200 bg-purple-50">
            <h2 className="text-lg font-semibold mb-4">Delivery Partner</h2>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <img
                  src={order.deliveryPersonDetails.profileImg || 'https://ui-avatars.com/api/?name=' + order.deliveryPersonDetails.name}
                  alt={order.deliveryPersonDetails.name}
                  className="h-16 w-16 rounded-full object-cover border-2 border-[#8a4af3]"
                />
              </div>
              <div className="ml-4">
                <h3 className="font-medium text-gray-900">{order.deliveryPersonDetails.name}</h3>
                <div className="mt-1 flex items-center">
                  <svg className="h-5 w-5 text-[#8a4af3]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <span className="ml-2 text-gray-600">{order.deliveryPersonDetails.mobileNumber}</span>
                </div>
                <div className="mt-1 flex items-center">
                  <svg className="h-5 w-5 text-[#8a4af3]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <span className="ml-2 text-gray-600">
                    {order.deliveryPersonDetails.vehicleType} {order.deliveryPersonDetails.vehicleNumber && `(${order.deliveryPersonDetails.vehicleNumber})`}
                  </span>
                </div>
                <div className="mt-1 flex items-center">
                  <svg className="h-5 w-5 text-[#8a4af3]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="ml-2 text-gray-600">
                    Expected delivery by {formatExpectedDelivery(order.expectedDeliveryTime)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Order Details */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Order Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-gray-600 mb-1">Order Date</p>
              <p className="font-medium">{formatDate(order.createdAt)}</p>
            </div>
            <div>
              <p className="text-gray-600 mb-1">Payment Method</p>
              <p className="font-medium">{order.paymentMethod}</p>
            </div>
            <div>
              <p className="text-gray-600 mb-1">Payment Status</p>
              <p className="font-medium">{order.paymentStatus}</p>
            </div>
            <div>
              <p className="text-gray-600 mb-1">Total Amount</p>
              <p className="font-medium">₹{order.totalAmount.toLocaleString()}</p>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Order Items</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {order.orderItems.map((item, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img className="h-10 w-10 rounded-md object-cover" src={item.imageUrl} alt={item.name} />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{Array.isArray(item.name) ? item.name[0] : item.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ₹{item.price.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ₹{item.totalPrice.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50">
                <tr>
                  <td colSpan="3" className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    Subtotal
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                    ₹{order.subtotal.toLocaleString()}
                  </td>
                </tr>
                <tr>
                  <td colSpan="3" className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    Delivery Fee
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                    ₹{order.deliveryFee.toLocaleString()}
                  </td>
                </tr>
                {order.discount > 0 && (
                  <tr>
                    <td colSpan="3" className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                      Discount
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                      -₹{order.discount.toLocaleString()}
                    </td>
                  </tr>
                )}
                <tr>
                  <td colSpan="3" className="px-6 py-4 text-right text-sm font-bold text-gray-900">
                    Total
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-bold">
                    ₹{order.totalAmount.toLocaleString()}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        {/* Delivery Address */}
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Delivery Address</h2>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium text-gray-900">{order.deliveryAddress.fullName}</p>
            <p className="text-gray-600">{order.deliveryAddress.mobileNumber}</p>
            <p className="text-gray-600 mt-2">
              {order.deliveryAddress.addressLine1}
              {order.deliveryAddress.addressLine2 && `, ${order.deliveryAddress.addressLine2}`}
            </p>
            <p className="text-gray-600">
              {order.deliveryAddress.city}, {order.deliveryAddress.state}, {order.deliveryAddress.pincode}
            </p>
            {order.deliveryAddress.landmark && (
              <p className="text-gray-600 mt-1">Landmark: {order.deliveryAddress.landmark}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderTracking;
