import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import config from '../../../config/environment.js';

const ShopkeeperDashboard = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [products, setProducts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [newQuantity, setNewQuantity] = useState('');

  // Get shopkeeper data from localStorage
  const [shopkeeperData, setShopkeeperData] = useState(null);

  // Check authentication and load shopkeeper data
  useEffect(() => {
    const isAuthenticated = localStorage.getItem('shopkeeperAuthenticated') === 'true';

    if (!isAuthenticated) {
      toast.error('Please login to access the dashboard');
      navigate('/work/shopkeeper/login');
      return;
    }

    // Get shopkeeper data from localStorage
    try {
      const shopkeeperDataStr = localStorage.getItem('shopkeeperData');
      if (shopkeeperDataStr) {
        const data = JSON.parse(shopkeeperDataStr);
        setShopkeeperData(data);
        console.log('Loaded shopkeeper data:', data);
      } else {
        // If no data is found, redirect to login
        toast.error('Session expired. Please login again');
        navigate('/work/shopkeeper/login');
        return;
      }
    } catch (error) {
      console.error('Error loading shopkeeper data:', error);
      toast.error('Error loading your profile. Please login again');
      navigate('/work/shopkeeper/login');
      return;
    }

    // Load products when component mounts
    fetchProducts();
  }, [navigate]);

  // Fetch products from shopgoods and shopitems
  const fetchProducts = async () => {
    try {
      setLoading(true);

      console.log('Fetching products from shopgoods and shopitems');

      let allProducts = [];

      // Fetch shop goods
      try {
        console.log(`Fetching shop goods from: ${config.API_BASE_URL}/shopgoods-api/shopGoods`);
        const shopGoodsResponse = await axios.get(`${config.API_BASE_URL}/shopgoods-api/shopGoods`);

        if (shopGoodsResponse.data && shopGoodsResponse.data.payload) {
          console.log('Shop goods loaded:', shopGoodsResponse.data.payload);

          // Transform shop goods to match our product format
          const shopGoods = shopGoodsResponse.data.payload.map(item => ({
            _id: item._id,
            name: Array.isArray(item.name) ? item.name : [item.name || 'Unnamed Product'],
            category: item.category || 'uncategorized',
            imageUrl: item.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image',
            description: item.description || '',
            quantity: item.quantity || 0,
            price: item.cost || 0, // Note: shop goods use 'cost' instead of 'price'
            productType: 'shopGood'
          }));

          console.log('Transformed shop goods:', shopGoods);
          allProducts = [...allProducts, ...shopGoods];
        }
      } catch (shopGoodsError) {
        console.error('Error fetching shop goods:', shopGoodsError);
        console.error('Shop goods error details:', shopGoodsError.response?.data || 'No response data');
      }

      // Fetch shop items
      try {
        console.log(`Fetching shop items from: ${config.API_BASE_URL}/shopitems-api/shopitems`);
        const shopItemsResponse = await axios.get(`${config.API_BASE_URL}/shopitems-api/shopitems`);

        if (shopItemsResponse.data && shopItemsResponse.data.payload) {
          console.log('Shop items loaded:', shopItemsResponse.data.payload);

          // Transform shop items to match our product format
          const shopItems = shopItemsResponse.data.payload.map(item => ({
            _id: item._id,
            name: Array.isArray(item.name) ? item.name : [item.name || 'Unnamed Product'],
            category: item.category || 'uncategorized',
            imageUrl: item.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image',
            description: item.description || '',
            quantity: item.quantity || 0,
            price: item.cost || 0, // Note: shop items use 'cost' instead of 'price'
            productType: 'shopItem'
          }));

          console.log('Transformed shop items:', shopItems);
          allProducts = [...allProducts, ...shopItems];
        }
      } catch (shopItemsError) {
        console.error('Error fetching shop items:', shopItemsError);
        console.error('Shop items error details:', shopItemsError.response?.data || 'No response data');
      }

      if (allProducts.length > 0) {
        console.log(`Loaded ${allProducts.length} products successfully`);
        setProducts(allProducts);
        setSearchResults(allProducts);
        toast.success(`Loaded ${allProducts.length} products successfully`);
      } else {
        // Fallback to mock data if no products were loaded
        console.log('No products found, using mock data as fallback');

        // For demo purposes, set some mock products
        const mockProducts = [
          {
            _id: '1',
            name: ['Tomato', 'Tamatar'],
            category: 'vegetables',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/tomato.jpg',
            description: 'Fresh red tomatoes',
            quantity: 50,
            price: 40,
            productType: 'shopItem'
          },
          {
            _id: '2',
            name: ['Onion', 'Pyaaz'],
            category: 'vegetables',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/onion.jpg',
            description: 'Fresh red onions',
            quantity: 0,
            price: 30,
            productType: 'shopItem'
          },
          {
            _id: '3',
            name: ['Apple', 'Seb'],
            category: 'fruits',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/apple.jpg',
            description: 'Fresh red apples',
            quantity: 20,
            price: 120,
            productType: 'shopItem'
          },
          {
            _id: '4',
            name: ['Rice', 'Chawal'],
            category: 'grains',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/rice.jpg',
            description: 'Premium basmati rice',
            quantity: 30,
            price: 60,
            productType: 'shopGood'
          },
          {
            _id: '5',
            name: ['Wheat Flour', 'Atta'],
            category: 'grains',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/flour.jpg',
            description: 'Whole wheat flour',
            quantity: 100,
            price: 20,
            productType: 'shopGood'
          }
        ];

        setProducts(mockProducts);
        setSearchResults(mockProducts);
        toast.info('Using demo products (API unavailable)');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults(products);
      return;
    }

    const query = searchQuery.toLowerCase();
    const results = products.filter(product => {
      // Check if query matches any name in the name array
      const nameMatch = product.name.some(name =>
        name.toLowerCase().includes(query)
      );

      // Check if query matches category
      const categoryMatch = product.category.toLowerCase().includes(query);

      // Check if query matches description
      const descriptionMatch = product.description.toLowerCase().includes(query);

      return nameMatch || categoryMatch || descriptionMatch;
    });

    setSearchResults(results);
  };

  // Handle quantity update
  const handleUpdateQuantity = async () => {
    if (!selectedProduct) return;

    try {
      setLoading(true);

      // Parse the new quantity
      const parsedQuantity = parseInt(newQuantity, 10);

      // Validate the quantity
      if (isNaN(parsedQuantity) || parsedQuantity < 0) {
        toast.error('Please enter a valid quantity (0 or greater)');
        return;
      }

      console.log(`Updating product ${selectedProduct._id} quantity to ${parsedQuantity}`);
      console.log('Product type:', selectedProduct.productType);

      // Determine which API endpoint to use based on product type
      const apiEndpoint = selectedProduct.productType === 'shopGood'
        ? 'shopgoods-api'
        : 'shopitems-api';

      try {
        // Call the API to update the product quantity
        console.log(`Calling API: ${config.API_BASE_URL}/${apiEndpoint}/update-quantity/${selectedProduct._id}`);
        console.log('Request body:', { quantity: parsedQuantity });

        const response = await axios.put(`${config.API_BASE_URL}/${apiEndpoint}/update-quantity/${selectedProduct._id}`, {
          quantity: parsedQuantity
        });

        if (response.data && response.data.message) {
          console.log('API response:', response.data);

          // Update the local state with the updated product
          const updatedProducts = products.map(product => {
            if (product._id === selectedProduct._id) {
              return {
                ...product,
                quantity: parsedQuantity
              };
            }
            return product;
          });

          setProducts(updatedProducts);
          setSearchResults(updatedProducts);

          const productName = selectedProduct.name && selectedProduct.name.length > 0
            ? selectedProduct.name[0]
            : 'Product';

          toast.success(`${productName} quantity updated successfully`);

          // Refresh the products list to get the latest data from the server
          setTimeout(() => {
            fetchProducts();
          }, 1000);

          // Close the modal
          setSelectedProduct(null);
          setNewQuantity('');
        }
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If the update-quantity endpoint fails, try the general update endpoint
        try {
          console.log('Trying general update endpoint');

          const productType = selectedProduct.productType === 'shopGood'
            ? 'shopgood'
            : 'shopitem';

          // Get the current product
          console.log(`Fetching product: ${config.API_BASE_URL}/${apiEndpoint}/${productType}/${selectedProduct._id}`);
          const getResponse = await axios.get(`${config.API_BASE_URL}/${apiEndpoint}/${productType}/${selectedProduct._id}`);

          if (getResponse.data && getResponse.data.payload) {
            const currentProduct = getResponse.data.payload;
            console.log('Current product:', currentProduct);

            // Update the quantity
            currentProduct.quantity = parsedQuantity;

            // Save the updated product
            console.log(`Updating product: ${config.API_BASE_URL}/${apiEndpoint}/${productType}/${selectedProduct._id}`);
            console.log('Update data:', currentProduct);

            const updateResponse = await axios.put(`${config.API_BASE_URL}/${apiEndpoint}/${productType}/${selectedProduct._id}`, currentProduct);

            if (updateResponse.data && updateResponse.data.message) {
              console.log('General update successful:', updateResponse.data);

              // Update the local state
              const updatedProducts = products.map(product => {
                if (product._id === selectedProduct._id) {
                  return {
                    ...product,
                    quantity: parsedQuantity
                  };
                }
                return product;
              });

              setProducts(updatedProducts);
              setSearchResults(updatedProducts);

              const productName = selectedProduct.name && selectedProduct.name.length > 0
                ? selectedProduct.name[0]
                : 'Product';

              toast.success(`${productName} quantity updated successfully`);

              // Refresh the products list to get the latest data from the server
              setTimeout(() => {
                fetchProducts();
              }, 1000);

              // Close the modal
              setSelectedProduct(null);
              setNewQuantity('');
            }
          }
        } catch (fallbackError) {
          console.error('General update failed:', fallbackError);
          console.error('Fallback error details:', fallbackError.response?.data || 'No response data');

          // Show a more specific error message based on the error
          if (apiError.response?.status === 404) {
            toast.error('Product not found in database. It may have been deleted.');
          } else if (apiError.response?.status === 400) {
            toast.error(`Invalid request: ${apiError.response.data.message || 'Unknown error'}`);
          } else {
            toast.error('Failed to update product quantity in database');
          }
        }
      }
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast.error('Failed to update quantity');
    } finally {
      setLoading(false);
    }
  };



  // Handle logout
  const handleLogout = () => {
    // Clear authentication state
    localStorage.removeItem('shopkeeperAuthenticated');
    localStorage.removeItem('shopkeeperData');

    // Show success message
    toast.success('Logged out successfully');

    // Navigate to work page
    navigate('/work');
  };

  return (
    <div className="container mx-auto px-4 py-8 mt-16">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Shopkeeper Dashboard</h1>
            <p className="text-gray-600">Manage your inventory and products</p>
          </div>

          {shopkeeperData && (
            <div className="flex items-center">
              <div className="mr-4 text-right">
                <p className="font-medium">{shopkeeperData.firstName || 'Shopkeeper'}</p>
                <p className="text-sm text-gray-600">{shopkeeperData.shopName || 'My Shop'}</p>
              </div>
              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                Logout
              </button>
            </div>
          )}
        </div>

        {/* Heading */}
        <div className="border-b mb-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Inventory Management</h2>
          <p className="text-gray-600">Update product quantities and manage stock</p>
        </div>

        {/* Main Content */}
        <div>
          <div>
            {/* Dashboard Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-blue-700">Total Products</h3>
                <p className="text-2xl font-bold">{products.length}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-green-700">In Stock</h3>
                <p className="text-2xl font-bold">{products.filter(p => p.quantity > 0).length}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-red-700">Out of Stock</h3>
                <p className="text-2xl font-bold">{products.filter(p => p.quantity <= 0).length}</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-purple-700">Categories</h3>
                <p className="text-2xl font-bold">{new Set(products.map(p => p.category)).size}</p>
              </div>
            </div>

            {/* Search Bar */}
            <div className="flex mb-6">
              <input
                type="text"
                placeholder="Search products by name, category or description..."
                className="flex-1 p-2 border rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary-custom"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button
                className="bg-primary-custom text-white px-4 py-2 rounded-r-md hover:bg-opacity-90"
                onClick={handleSearch}
              >
                Search
              </button>
            </div>

            {/* Products Grid */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">Products ({searchResults.length})</h3>
                <div className="flex space-x-2">
                  <select
                    className="p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary-custom"
                    onChange={(e) => {
                      if (e.target.value === 'all') {
                        setSearchResults(products);
                      } else {
                        setSearchResults(products.filter(p => p.category === e.target.value));
                      }
                    }}
                  >
                    <option value="all">All Categories</option>
                    {Array.from(new Set(products.map(p => p.category))).map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  <select
                    className="p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary-custom"
                    onChange={(e) => {
                      const value = e.target.value;
                      let sortedProducts = [...searchResults];

                      if (value === 'name-asc') {
                        sortedProducts.sort((a, b) => {
                          const nameA = a.name && a.name.length > 0 ? a.name[0] : '';
                          const nameB = b.name && b.name.length > 0 ? b.name[0] : '';
                          return nameA.localeCompare(nameB);
                        });
                      } else if (value === 'name-desc') {
                        sortedProducts.sort((a, b) => {
                          const nameA = a.name && a.name.length > 0 ? a.name[0] : '';
                          const nameB = b.name && b.name.length > 0 ? b.name[0] : '';
                          return nameB.localeCompare(nameA);
                        });
                      } else if (value === 'price-asc') {
                        sortedProducts.sort((a, b) => a.price - b.price);
                      } else if (value === 'price-desc') {
                        sortedProducts.sort((a, b) => b.price - a.price);
                      } else if (value === 'quantity-asc') {
                        sortedProducts.sort((a, b) => a.quantity - b.quantity);
                      } else if (value === 'quantity-desc') {
                        sortedProducts.sort((a, b) => b.quantity - a.quantity);
                      }

                      setSearchResults(sortedProducts);
                    }}
                  >
                    <option value="">Sort By</option>
                    <option value="name-asc">Name (A-Z)</option>
                    <option value="name-desc">Name (Z-A)</option>
                    <option value="price-asc">Price (Low to High)</option>
                    <option value="price-desc">Price (High to Low)</option>
                    <option value="quantity-asc">Quantity (Low to High)</option>
                    <option value="quantity-desc">Quantity (High to Low)</option>
                  </select>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-custom"></div>
                  <p className="ml-3 text-gray-600">Loading products...</p>
                </div>
              ) : searchResults.length === 0 ? (
                <div className="bg-white rounded-lg shadow-md p-8 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-500">Try adjusting your search or filter criteria</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {searchResults.map(product => (
                    <div key={product._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <div className="relative h-48 overflow-hidden bg-gray-200">
                        <img
                          src={product.imageUrl}
                          alt={product.name && product.name.length > 0 ? product.name[0] : 'Product Image'}
                          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';
                          }}
                        />
                        <div className="absolute top-2 right-2">
                          {product.quantity > 0 ? (
                            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                              In Stock
                            </span>
                          ) : (
                            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                              Out of Stock
                            </span>
                          )}
                        </div>
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                          <span className="text-white text-xs font-medium uppercase tracking-wider">
                            {product.category}
                          </span>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-lg font-semibold text-gray-800 line-clamp-1">
                            {product.name && product.name.length > 0 ? product.name[0] : 'Unnamed Product'}
                          </h3>
                          <span className="text-lg font-bold text-primary-custom">₹{product.price}</span>
                        </div>
                        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                          {product.description ? product.description : 'No description available'}
                        </p>
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-500">
                            <span className="font-medium">Qty:</span> {product.quantity}
                          </div>
                          <button
                            className="px-3 py-1.5 bg-primary-custom text-white rounded-md hover:bg-opacity-90 transition-colors"
                            onClick={() => {
                              setSelectedProduct(product);
                              setNewQuantity(product.quantity.toString());
                            }}
                          >
                            Update
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Update Quantity Modal */}
            {selectedProduct && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
                  <h2 className="text-xl font-bold mb-4">Update Quantity</h2>
                  <div className="mb-4">
                    <div className="flex items-center mb-4">
                      <img
                        src={selectedProduct.imageUrl}
                        alt={selectedProduct.name && selectedProduct.name.length > 0 ? selectedProduct.name[0] : 'Product Image'}
                        className="w-16 h-16 object-cover rounded-md mr-4"
                      />
                      <div>
                        <div className="font-medium">{selectedProduct.name && selectedProduct.name.length > 0 ? selectedProduct.name[0] : 'Unnamed Product'}</div>
                        <div className="text-sm text-gray-500">Current Quantity: {selectedProduct.quantity}</div>
                      </div>
                    </div>
                    <label className="block text-gray-700 mb-2">New Quantity:</label>
                    <input
                      type="number"
                      min="0"
                      className="w-full p-2 border rounded focus:outline-none focus:ring-1 focus:ring-primary-custom"
                      value={newQuantity}
                      onChange={(e) => setNewQuantity(e.target.value)}
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <button
                      className="px-4 py-2 border rounded hover:bg-gray-100"
                      onClick={() => {
                        setSelectedProduct(null);
                        setNewQuantity('');
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      className="px-4 py-2 bg-primary-custom text-white rounded hover:bg-opacity-90"
                      onClick={handleUpdateQuantity}
                      disabled={loading}
                    >
                      {loading ? 'Updating...' : 'Update'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShopkeeperDashboard;
