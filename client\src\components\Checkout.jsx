import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { toast, Slide } from 'react-toastify';
import config from '../config/environment.js';
import { getCurrentAddress } from '../services/locationService';
import LocationPermission from './common/LocationPermission';
import { processCODPayment, PAYMENT_METHODS } from '../services/paymentService';

const Checkout = () => {
  const { cartItems, cartTotal, clearCart } = useCart();
  const { currentUser, openAuthModal } = useAuth();
  const navigate = useNavigate();

  // State for address
  const [address, setAddress] = useState({
    fullName: currentUser?.firstName + ' ' + (currentUser?.lastName || ''),
    mobileNumber: currentUser?.mobileNumber || '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    pincode: '',
    addressType: 'home',
    isDefault: true
  });

  // State for saved addresses
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [showSavedAddresses, setShowSavedAddresses] = useState(false);
  const [selectedAddressId, setSelectedAddressId] = useState(null);

  // State for payment
  const [paymentMethod, setPaymentMethod] = useState(PAYMENT_METHODS.COD);
  const [paymentDetails, setPaymentDetails] = useState(null);
  const [loading, setLoading] = useState(false);

  // Ensure COD is selected by default
  useEffect(() => {
    setPaymentMethod(PAYMENT_METHODS.COD);
  }, []);

  // State for location
  const [locationLoading, setLocationLoading] = useState(false);
  const [locationError, setLocationError] = useState(null);
  const [locationPermissionRequested, setLocationPermissionRequested] = useState(false);
  const [promptShown, setPromptShown] = useState(false);

  // Delivery fee and total calculation
  const deliveryFee = 40;
  const totalAmount = cartTotal + deliveryFee;

  // Update address when user changes and fetch saved addresses
  useEffect(() => {
    if (currentUser) {
      setAddress(prev => ({
        ...prev,
        fullName: currentUser.firstName + ' ' + (currentUser.lastName || ''),
        mobileNumber: currentUser.mobileNumber || ''
      }));

      // Fetch saved addresses
      fetchSavedAddresses();
    }
  }, [currentUser]);

  // Fetch saved addresses from the server
  const fetchSavedAddresses = async () => {
    if (!currentUser?._id) return;

    setLoadingAddresses(true);
    try {
      const response = await fetch(`${config.API_BASE_URL}/address-api/addresses/${currentUser._id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch addresses');
      }

      const data = await response.json();
      setSavedAddresses(data.payload || []);

      // If there's a default address, select it
      const defaultAddress = data.payload?.find(addr => addr.isDefault);
      if (defaultAddress) {
        setSelectedAddressId(defaultAddress._id);
        setAddress({
          fullName: defaultAddress.fullName,
          mobileNumber: defaultAddress.mobileNumber,
          addressLine1: defaultAddress.addressLine1,
          addressLine2: defaultAddress.addressLine2 || '',
          city: defaultAddress.city,
          state: defaultAddress.state,
          pincode: defaultAddress.pincode,
          addressType: defaultAddress.addressType,
          isDefault: defaultAddress.isDefault
        });
      }
    } catch (error) {
      console.error('Error fetching addresses:', error);
      // Don't show error toast as this is not critical
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Handle address input change
  const handleAddressChange = (e) => {
    const { name, value } = e.target;
    setAddress(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle payment method change - always set to COD
  const handlePaymentMethodChange = () => {
    // Always set to COD regardless of input
    setPaymentMethod(PAYMENT_METHODS.COD);
    setPaymentDetails(null); // Reset payment details when method changes

    // Show message for COD
    toast.info('Cash on Delivery selected - Pay when your order arrives', {
      toastId: 'payment-cod',
      position: "top-right",
      autoClose: 3000
    });
  };

  // Handle selecting a saved address
  const handleSelectAddress = (selectedAddress) => {
    setSelectedAddressId(selectedAddress._id);
    setAddress({
      fullName: selectedAddress.fullName,
      mobileNumber: selectedAddress.mobileNumber,
      addressLine1: selectedAddress.addressLine1,
      addressLine2: selectedAddress.addressLine2 || '',
      city: selectedAddress.city,
      state: selectedAddress.state,
      pincode: selectedAddress.pincode,
      addressType: selectedAddress.addressType,
      isDefault: selectedAddress.isDefault
    });

    // Set coordinates if available
    if (selectedAddress.coordinates) {
      setCoordinates(selectedAddress.coordinates);
    } else {
      setCoordinates(null);
    }

    setShowSavedAddresses(false);
  };

  // State for coordinates
  const [coordinates, setCoordinates] = useState(null);

  // Handle location request
  const handleLocationRequest = useCallback(async () => {
    // Dismiss any existing toasts to prevent multiple notifications
    toast.dismiss();

    // Set states
    setLocationLoading(true);
    setLocationError(null);
    setLocationPermissionRequested(true);
    setPromptShown(true); // Ensure prompt won't show again

    try {
      // Get current address from location service
      const currentAddress = await getCurrentAddress();

      // Update address state with location data
      setAddress(prev => ({
        ...prev,
        addressLine1: currentAddress.addressLine1 || prev.addressLine1,
        addressLine2: currentAddress.addressLine2 || prev.addressLine2,
        city: currentAddress.city || prev.city,
        state: currentAddress.state || prev.state,
        pincode: currentAddress.pincode || prev.pincode
      }));

      // Save coordinates for the order
      if (currentAddress.coordinates) {
        setCoordinates(currentAddress.coordinates);
        console.log('Coordinates saved for order tracking:', currentAddress.coordinates);
      }

      // Show success toast with unique ID to prevent duplicates
      toast.success('Location detected successfully!', {
        toastId: 'location-success',
        position: "top-right",
        transition: Slide
      });
    } catch (error) {
      console.error('Error getting location:', error);
      setLocationError(error.message);

      // Show error toast with unique ID to prevent duplicates
      toast.error(`Failed to get location: ${error.message}`, {
        toastId: 'location-error',
        position: "top-right",
        transition: Slide
      });
    } finally {
      setLocationLoading(false);
    }
  }, [setAddress, setLocationLoading, setLocationError, setLocationPermissionRequested, setPromptShown]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check if user is logged in
    if (!currentUser) {
      openAuthModal('login');
      return;
    }

    // Validate cart
    if (cartItems.length === 0) {
      toast.error('Your cart is empty');
      navigate('/cart');
      return;
    }

    // Validate address
    const requiredFields = ['fullName', 'mobileNumber', 'addressLine1', 'city', 'state', 'pincode'];
    for (const field of requiredFields) {
      if (!address[field]) {
        toast.error(`Please enter your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
        return;
      }
    }

    // Validate pincode
    if (!/^\d{6}$/.test(address.pincode)) {
      toast.error('Please enter a valid 6-digit pincode');
      return;
    }

    // Check if the user wants to save the address
    const saveAddressCheckbox = document.getElementById('saveAddress');
    if (saveAddressCheckbox && saveAddressCheckbox.checked && currentUser) {
      try {
        // Save the address to the user's account
        const response = await fetch(`${config.API_BASE_URL}/address-api/address`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...address,
            coordinates: coordinates || null,
            userId: currentUser._id
          }),
        });

        if (!response.ok) {
          console.error('Failed to save address');
        } else {
          console.log('Address saved successfully');
        }
      } catch (error) {
        console.error('Error saving address:', error);
      }
    }

    // Validate mobile number
    if (!/^\d{10}$/.test(address.mobileNumber.replace(/[^0-9]/g, ''))) {
      toast.error('Please enter a valid 10-digit mobile number');
      return;
    }

    setLoading(true);

    try {
      // Format order items
      const orderItems = cartItems.map(item => ({
        productId: item._id || item.productId,
        productType: item.productType || 'shopItem',
        name: Array.isArray(item.name) ? item.name[0] : item.name,
        imageUrl: item.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image',
        price: item.price || item.cost,
        quantity: item.quantity,
        totalPrice: (item.price || item.cost) * item.quantity
      }));

      // Prepare order details
      const orderDetails = {
        userId: currentUser._id,
        items: orderItems,
        address: address,
        subtotal: cartTotal,
        deliveryFee,
        totalAmount,
        coordinates: coordinates
      };

      // Always use Cash on Delivery payment method
      let paymentResult;

      // Set payment method to COD
      setPaymentMethod(PAYMENT_METHODS.COD);

      // Process Cash on Delivery payment
      paymentResult = await processCODPayment(orderDetails);
      console.log('COD payment processed:', paymentResult);

      // Save payment details
      setPaymentDetails(paymentResult);

      // Create order
      const response = await fetch(`${config.API_BASE_URL}/order-api/order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: currentUser._id,
          orderItems,
          deliveryAddress: {
            ...address,
            coordinates: coordinates || null
          },
          // Log coordinates for debugging
          customerCoordinates: coordinates ? {
            latitude: coordinates.latitude,
            longitude: coordinates.longitude
          } : null,
          paymentMethod: PAYMENT_METHODS.COD,
          paymentStatus: paymentResult.paymentStatus,
          paymentId: paymentResult.paymentId,
          subtotal: cartTotal,
          deliveryFee,
          discount: 0,
          totalAmount
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to place order');
      }

      const data = await response.json();

      // Clear cart after successful order
      clearCart();

      // Show success message
      toast.success('Order placed successfully!');

      // Redirect to order confirmation page
      navigate(`/order-confirmation/${data.payload._id}`);
    } catch (error) {
      console.error('Error placing order:', error);
      toast.error(error.message || 'Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // If not logged in, redirect to login
  useEffect(() => {
    if (!currentUser) {
      openAuthModal('login');
    }
  }, [currentUser, openAuthModal]);

  // If cart is empty, redirect to cart page
  useEffect(() => {
    if (cartItems.length === 0) {
      navigate('/cart');
    }
  }, [cartItems, navigate]);

  // Show location prompt when page loads - only once
  useEffect(() => {
    // Only show the prompt if the address fields are empty and it hasn't been shown yet
    if (
      currentUser &&
      !address.addressLine1 &&
      !address.city &&
      !address.state &&
      !address.pincode &&
      !locationPermissionRequested &&
      !promptShown
    ) {
      // Mark prompt as shown to prevent duplicates
      setPromptShown(true);

      // Use a single toast notification instead of multiple
      toast.dismiss(); // Dismiss any existing toasts first

      const toastId = toast.info(
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="text-blue-500 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <p className="font-medium">Allow location access?</p>
              <p className="text-sm">We can auto-fill your delivery address</p>
            </div>
          </div>
          <div className="flex space-x-2 ml-4">
            <button
              onClick={() => {
                toast.dismiss(toastId);
                setLocationPermissionRequested(true);
              }}
              className="px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300 transition-colors"
            >
              No thanks
            </button>
            <button
              onClick={() => {
                toast.dismiss(toastId);
                handleLocationRequest();
              }}
              className="px-3 py-1 text-sm bg-primary-custom text-white rounded hover:bg-opacity-90 transition-colors"
            >
              Yes, use my location
            </button>
          </div>
        </div>,
        {
          autoClose: 8000, // Auto close after 8 seconds
          closeButton: true,
          closeOnClick: false,
          draggable: true,
          position: "top-center", // Position at top center
          className: "!max-w-xl", // Make toast wider
          toastId: "location-prompt", // Unique ID to prevent duplicates
          transition: Slide
        }
      );
    }
  }, [currentUser, address, locationPermissionRequested, handleLocationRequest, promptShown]);

  return (
    <div className="container mx-auto px-4 py-6 mt-16">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 font-[Gilroy,_Arial,_Helvetica_Neue,_sans-serif]">
          Checkout
        </h1>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left column - Delivery Address */}
        <div className="lg:w-2/3">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">Delivery Address</h2>

            {/* Saved Addresses Section */}
            {currentUser && savedAddresses.length > 0 && (
              <div className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-md font-medium text-gray-700">Saved Addresses</h3>
                  <button
                    type="button"
                    onClick={() => setShowSavedAddresses(!showSavedAddresses)}
                    className="text-primary-custom text-sm flex items-center"
                  >
                    {showSavedAddresses ? 'Hide' : 'Show'}
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ml-1 transition-transform ${showSavedAddresses ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>

                {showSavedAddresses && (
                  <div className="space-y-3 max-h-60 overflow-y-auto p-2 border border-gray-200 rounded-md">
                    {loadingAddresses ? (
                      <div className="flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-custom"></div>
                      </div>
                    ) : (
                      savedAddresses.map((savedAddress) => (
                        <div
                          key={savedAddress._id}
                          onClick={() => handleSelectAddress(savedAddress)}
                          className={`p-3 border rounded-md cursor-pointer transition-colors ${
                            selectedAddressId === savedAddress._id
                              ? 'border-primary-custom bg-primary-custom/5'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex justify-between">
                            <div className="font-medium">{savedAddress.fullName}</div>
                            <div className="text-sm text-gray-500">{savedAddress.addressType}</div>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">{savedAddress.mobileNumber}</div>
                          <div className="text-sm text-gray-600 mt-1">
                            {savedAddress.addressLine1}
                            {savedAddress.addressLine2 && `, ${savedAddress.addressLine2}`}
                          </div>
                          <div className="text-sm text-gray-600">
                            {savedAddress.city}, {savedAddress.state}, {savedAddress.pincode}
                          </div>
                          {savedAddress.isDefault && (
                            <div className="mt-2">
                              <span className="text-xs bg-primary-custom/10 text-primary-custom px-2 py-1 rounded-full">
                                Default
                              </span>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Location Permission Component - only show if not already requested */}
            {!locationPermissionRequested && (
              <LocationPermission
                onRequestLocation={handleLocationRequest}
                loading={locationLoading}
                error={locationError}
              />
            )}

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="col-span-2 md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                  <input
                    type="text"
                    name="fullName"
                    value={address.fullName}
                    onChange={handleAddressChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom"
                    required
                  />
                </div>

                <div className="col-span-2 md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
                  <input
                    type="tel"
                    name="mobileNumber"
                    value={address.mobileNumber}
                    onChange={handleAddressChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom"
                    required
                  />
                </div>

                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                  <div className="relative">
                    <input
                      type="text"
                      name="addressLine1"
                      value={address.addressLine1}
                      onChange={handleAddressChange}
                      placeholder="House/Flat No., Building Name, Street"
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom ${
                        locationPermissionRequested && address.addressLine1 ? 'border-green-500 bg-green-50' : 'border-gray-300'
                      }`}
                      required
                    />
                    {locationPermissionRequested && address.addressLine1 && (
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>

                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                  <input
                    type="text"
                    name="addressLine2"
                    value={address.addressLine2}
                    onChange={handleAddressChange}
                    placeholder="Landmark, Area"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                  <div className="relative">
                    <input
                      type="text"
                      name="city"
                      value={address.city}
                      onChange={handleAddressChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom ${
                        locationPermissionRequested && address.city ? 'border-green-500 bg-green-50' : 'border-gray-300'
                      }`}
                      required
                    />
                    {locationPermissionRequested && address.city && (
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                  <div className="relative">
                    <input
                      type="text"
                      name="state"
                      value={address.state}
                      onChange={handleAddressChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom ${
                        locationPermissionRequested && address.state ? 'border-green-500 bg-green-50' : 'border-gray-300'
                      }`}
                      required
                    />
                    {locationPermissionRequested && address.state && (
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
                  <div className="relative">
                    <input
                      type="text"
                      name="pincode"
                      value={address.pincode}
                      onChange={handleAddressChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom ${
                        locationPermissionRequested && address.pincode ? 'border-green-500 bg-green-50' : 'border-gray-300'
                      }`}
                      required
                      maxLength={6}
                    />
                    {locationPermissionRequested && address.pincode && (
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address Type</label>
                  <select
                    name="addressType"
                    value={address.addressType}
                    onChange={handleAddressChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-custom focus:border-primary-custom"
                  >
                    <option value="home">Home</option>
                    <option value="work">Work</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                {/* Save Address Checkbox and Manage Addresses Link */}
                {currentUser && (
                  <div className="col-span-2 flex justify-between items-center mt-6">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="saveAddress"
                        name="saveAddress"
                        className="h-4 w-4 text-primary-custom focus:ring-primary-custom border-gray-300 rounded"
                      />
                      <label htmlFor="saveAddress" className="ml-2 block text-sm text-gray-700">
                        Save this address for future orders
                      </label>
                    </div>

                    <Link
                      to="/account"
                      state={{ activeTab: 'addresses' }}
                      className="text-primary-custom text-sm hover:underline"
                    >
                      Manage saved addresses
                    </Link>
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* Payment Method */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-5">Payment Method</h2>

            {/* Direct Cash on Delivery Option */}
            <div
              className="border-2 rounded-lg p-5 cursor-pointer transition-all duration-200 border-[#8a4af3] bg-[#8a4af3]/5"
              onClick={() => handlePaymentMethodChange(PAYMENT_METHODS.COD)}
            >
              <div className="flex items-center">
                {/* Cash Icon */}
                <div className="w-10 h-10 mr-4 flex-shrink-0 bg-[#8a4af3]/10 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#8a4af3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                  </svg>
                </div>

                {/* Payment Details */}
                <div className="flex-1">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <h4 className="font-bold text-gray-800 text-lg">Cash on Delivery</h4>
                      <span className="ml-2 text-xs bg-[#8a4af3]/10 text-[#8a4af3] px-2 py-0.5 rounded-full font-medium">Recommended</span>
                    </div>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full font-medium">Free</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Pay when your order is delivered</p>
                </div>

                {/* Radio Button */}
                <div className="ml-4 flex-shrink-0">
                  <div className="w-6 h-6 rounded-full border-2 border-[#8a4af3] flex items-center justify-center">
                    <div className="w-3 h-3 rounded-full bg-[#8a4af3]"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Details (shown after payment is processed) */}
            {paymentDetails && (
              <div className="mt-4 p-4 bg-[#8a4af3]/5 border border-[#8a4af3]/20 rounded-md">
                <div className="flex items-center text-[#8a4af3] mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="font-medium">Payment Method Selected</span>
                </div>
                <p className="text-sm text-gray-600">
                  You will pay the full amount when your order is delivered.
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Payment ID: {paymentDetails.paymentId}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right column - Order Summary */}
        <div className="lg:w-1/3">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-20">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

            <div className="max-h-60 overflow-y-auto mb-4">
              {cartItems.map((item, index) => {
                // Handle different data structures
                let displayName = 'Product';
                if (Array.isArray(item.name) && item.name.length > 0) {
                  displayName = item.name[0];
                } else if (typeof item.name === 'string') {
                  displayName = item.name;
                }

                // Use a default image if imageUrl is missing
                const imageUrl = item.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image';

                // Handle different price properties
                const price = item.price || item.cost || 0;

                return (
                  <div key={item._id || index} className="flex items-center mb-3 pb-3 border-b border-gray-200 last:border-b-0 last:pb-0 last:mb-0">
                    <div className="w-12 h-12 flex-shrink-0">
                      <img
                        src={imageUrl}
                        alt={displayName}
                        className="w-full h-full object-contain rounded-md"
                        loading="lazy"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = 'https://via.placeholder.com/300x200?text=Error+Loading+Image';
                        }}
                      />
                    </div>
                    <div className="ml-3 flex-grow">
                      <p className="text-sm font-medium text-gray-800 truncate">{displayName}</p>
                      <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                    </div>
                    <div className="text-sm font-medium">₹{(price * item.quantity).toFixed(2)}</div>
                  </div>
                );
              })}
            </div>

            {/* Payment Summary */}
            <div className="payment-summary bg-white rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-4">Payment Summary</h3>

              {/* Cash on Delivery Method Display */}
              <div className="mb-4 p-3 bg-[#8a4af3]/5 border border-[#8a4af3]/20 rounded-md">
                <div className="flex items-center">
                  <div className="w-8 h-8 mr-3 flex-shrink-0 bg-[#8a4af3]/10 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#8a4af3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-800">Cash on Delivery</h4>
                    <p className="text-xs text-gray-600">Pay when your order is delivered</p>
                  </div>
                  <span className="ml-auto text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full font-medium">Free</span>
                </div>
              </div>

              {/* Order Summary */}
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">₹{cartTotal.toFixed(2)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Delivery Fee</span>
                  <span className="font-medium">₹{deliveryFee.toFixed(2)}</span>
                </div>

                <div className="border-t border-gray-200 my-2 pt-2"></div>

                <div className="flex justify-between">
                  <span className="font-semibold text-base">Total</span>
                  <span className="font-semibold text-[#8a4af3] text-lg">₹{totalAmount.toFixed(2)}</span>
                </div>

                {/* Payment Note */}
                <div className="mt-2 text-xs text-gray-600 flex items-center bg-gray-50 p-2 rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-[#8a4af3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Pay the full amount when your order is delivered
                </div>
              </div>
            </div>

            <button
              onClick={handleSubmit}
              disabled={loading}
              className="w-full mt-6 bg-[#8a4af3] text-white py-3 rounded-md hover:bg-opacity-90 transition-all duration-300 font-medium disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {loading ? 'Processing...' : `Place Order • ₹${totalAmount.toFixed(2)}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
