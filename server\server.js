const exp = require("express");
const app = exp();
require('dotenv').config();
const mongoose = require("mongoose");
const http = require('http');
const { initializeSocket } = require('./services/socketService');
const userApp = require("./APIs/user.js");
const shopApp = require("./APIs/shopkeeper.js");
const workerApp = require("./APIs/worker.js");
const deliveryApp = require("./APIs/deliveryPerson.js");
const shopItemsApp = require("./APIs/shopItems.js");
const shopGoodsApp = require("./APIs/shopGoods.js");
const vendorApp = require("./APIs/Vendor.js");
const worksApp = require("./APIs/works.js");
const orderApp = require("./APIs/order.js");
const addressApp = require("./APIs/address.js");
const favoriteApp = require("./APIs/favorite.js");
const cartApp = require("./APIs/cart.js");
const cors = require('cors');

// Configure CORS for development and production
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? [process.env.FRONTEND_URL, 'https://homexpert-delivery.vercel.app']
    : ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));
const port = process.env.PORT || 3000;

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.IO with the HTTP server
initializeSocket(server);

//data base connection
mongoose.connect(process.env.DB_URL)
.then(() => {
    server.listen(port, () => console.log(`Server listening on port ${port}..`));
    console.log("Data Base Connection Success");
})
.catch(err => console.log("Error in DB Connection", err));

//body parser middleware
app.use(exp.json())
//API routes
app.use('/user-api',userApp)
app.use('/shopkeeper-api',shopApp)
app.use('/worker-api',workerApp)
app.use('/delivery-api',deliveryApp)
app.use('/shopitems-api',shopItemsApp)
app.use('/shopgoods-api',shopGoodsApp)
app.use('/vendor-api',vendorApp)
app.use('/works-api',worksApp)
app.use('/order-api',orderApp)
app.use('/address-api',addressApp)
app.use('/favorite-api',favoriteApp)
app.use('/cart-api',cartApp)
// error handler - simplified version
app.use((err, req, res, next) => {
    console.log("Error in express error handler:", err);

    // Handle mongoose validation errors
    if (err.name === 'ValidationError') {
        return res.status(400).send({
            message: 'Validation error: ' + Object.values(err.errors).map(e => e.message).join(', ')
        });
    }

    // Handle duplicate key errors
    if (err.code === 11000) {
        console.log("Duplicate key error details:", JSON.stringify(err, null, 2));

        // Get the field name from the error
        let field = 'field';
        if (err.keyPattern) {
            field = Object.keys(err.keyPattern)[0];
        } else if (err.keyValue) {
            field = Object.keys(err.keyValue)[0];
        }

        // Create a more user-friendly message based on the field
        if (field === 'mobileNumber') {
            return res.status(409).send({
                message: 'An account with this phone number already exists. Please login instead.'
            });
        } else if (field === 'email') {
            return res.status(409).send({
                message: 'An account with this email already exists. Please use a different email.'
            });
        } else if (field === 'userId') {
            // This should not happen anymore, but just in case
            console.error('userId duplicate key error - this should not happen with our fix');
            return res.status(500).send({
                message: 'An unexpected error occurred. Please try again.'
            });
        } else {
            return res.status(409).send({
                message: 'An account with this information already exists. Please try again with different information.'
            });
        }
    }

    // Default error response
    res.status(err.status || 500).send({
        message: err.message || 'An unexpected error occurred'
    });
})

// Export the app for Vercel
module.exports = app;