import React from 'react'
import { createBrowserRouter, Navigate, RouterProvider } from 'react-router-dom'
import RootLayout from './components/RootLayout'
import Home from './components/Home'
import Cart from './components/Cart'
import Products from './components/Products'
import UserDashboard from './components/user/UserDashboard'
import AuthModal from './components/auth/AuthModal'
import Checkout from './components/Checkout'
import OrderConfirmation from './components/OrderConfirmation'
import ErrorBoundary from './components/common/ErrorBoundary'
import WorkAtHomeXpert from './components/work/WorkAtHomeXpert'
import DeliveryDashboard from './components/work/delivery/DeliveryDashboard'
import ShopkeeperDashboard from './components/work/shopkeeper/ShopkeeperDashboard'
import ShopkeeperLogin from './components/work/shopkeeper/ShopkeeperLogin'

import EnhancedDeliveryLogin from './components/work/delivery/auth/EnhancedDeliveryLogin'
import EnhancedDeliveryRegister from './components/work/delivery/auth/EnhancedDeliveryRegister'
import AdminLogin from './components/admin/AdminLogin'
import AdminDashboard from './components/admin/AdminDashboard'
import './App.css';
import { useAuth } from './context/AuthContext'
import { useDeliveryAuth } from './context/DeliveryAuthContext'

// Protected route component for regular users
const ProtectedRoute = ({ children }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/" replace />;
  }

  return children;
};

// Protected route component for delivery partners
const DeliveryProtectedRoute = ({ children }) => {
  const { currentDeliveryPartner, loading } = useDeliveryAuth();

  console.log('DeliveryProtectedRoute - currentDeliveryPartner:', currentDeliveryPartner);

  if (loading) {
    return <div className="flex items-center justify-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8a4af3]"></div>
    </div>;
  }

  if (!currentDeliveryPartner) {
    console.log('No delivery partner found, redirecting to login');
    return <Navigate to="/work/delivery/login" replace />;
  }

  return children;
};

// Protected route component for shopkeepers
// For now, this is a mock implementation since we don't have a separate auth context for shopkeepers
const ShopkeeperProtectedRoute = ({ children }) => {
  // In a real implementation, this would use a shopkeeper auth context
  // For now, we'll use a mock implementation that always redirects to login
  const isAuthenticated = localStorage.getItem('shopkeeperAuthenticated') === 'true';

  if (!isAuthenticated) {
    return <Navigate to="/work/shopkeeper/login" replace />;
  }

  return children;
};

function App() {
  const browserRouterObj = createBrowserRouter([
    {
      path: "/",
      element: <RootLayout />,
      children: [
        {
          path: "",
          element: <Home />
        },
        {
          path: "cart",
          element: <Cart />
        },
        {
          path: "checkout",
          element: <ProtectedRoute><Checkout /></ProtectedRoute>
        },
        {
          path: "order-confirmation/:orderId",
          element: <ProtectedRoute><OrderConfirmation /></ProtectedRoute>
        },
        {
          path: "products",
          element: <Products />
        },
        {
          path: "account",
          element: <ProtectedRoute><UserDashboard /></ProtectedRoute>
        },
        {
          path: "work",
          element: <WorkAtHomeXpert />
        },

        {
          path: "work/delivery/login",
          element: <EnhancedDeliveryLogin />
        },
        {
          path: "work/delivery/register",
          element: <EnhancedDeliveryRegister />
        },
        {
          path: "work/delivery",
          element: <DeliveryProtectedRoute><ErrorBoundary><DeliveryDashboard /></ErrorBoundary></DeliveryProtectedRoute>
        },
        {
          path: "work/shopkeeper/login",
          element: <ShopkeeperLogin />
        },
        {
          path: "work/shopkeeper/dashboard",
          element: <ShopkeeperProtectedRoute><ShopkeeperDashboard /></ShopkeeperProtectedRoute>
        },
        {
          path: "admin/login",
          element: <AdminLogin />
        },
        {
          path: "admin/dashboard",
          element: <AdminDashboard />
        }
      ]
    }
  ])
  return (
    <ErrorBoundary>
      <div className="relative">
        <RouterProvider router={browserRouterObj} />
        <AuthModal />
      </div>
    </ErrorBoundary>
  )
}

export default App




