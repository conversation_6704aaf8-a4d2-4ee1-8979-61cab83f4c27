# Environment Variables Setup for HomeXpert

This document explains how to set up environment variables for both development and production environments.

## Client Environment Variables

### Development (.env)

The client uses Vite, so all environment variables must be prefixed with `VITE_`.

```bash
# Development Environment Variables for HomeXpert Client

# API Base URL for development
VITE_API_BASE_URL=http://localhost:3000

# Environment
NODE_ENV=development

# Socket.IO URL for development
VITE_SOCKET_URL=http://localhost:3000

# Database URL (for reference - used by server)
VITE_DB_URL=mongodb+srv://user1:<EMAIL>/homeexpert?retryWrites=true&w=majority&appName=Cluster0

# Clerk Authentication (if using Clerk)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_cnVsaW5nLWphY2thbC05LmNsZXJrLmFjY291bnRzLmRldiQ

# Google Maps API Key (if needed)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# App Configuration
VITE_APP_NAME=HomeXpert
VITE_APP_VERSION=1.0.0

# Development flags
VITE_DEBUG=true
VITE_ENABLE_LOGGING=true

# API Endpoints (for easy switching)
VITE_USER_API=/user-api
VITE_SHOPKEEPER_API=/shopkeeper-api
VITE_WORKER_API=/worker-api
VITE_DELIVERY_API=/delivery-api
VITE_SHOPITEMS_API=/shopitems-api
VITE_SHOPGOODS_API=/shopgoods-api
VITE_VENDOR_API=/vendor-api
VITE_WORKS_API=/works-api
VITE_ORDER_API=/order-api
VITE_ADDRESS_API=/address-api
VITE_FAVORITE_API=/favorite-api
VITE_CART_API=/cart-api

# Feature Flags
VITE_ENABLE_SOCKET=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_LOCATION_TRACKING=true
```

### Production (.env.production)

```bash
# Production Environment Variables for HomeXpert Client

# API Base URL - This will be your Vercel deployment URL
VITE_API_BASE_URL=https://your-app-name.vercel.app/api

# Environment
NODE_ENV=production

# Clerk Authentication (if using Clerk)
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_live_publishable_key_here

# Google Maps API Key (if needed)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Socket.IO URL (same as API base URL for production)
VITE_SOCKET_URL=https://your-app-name.vercel.app

# App Configuration
VITE_APP_NAME=HomeXpert
VITE_APP_VERSION=1.0.0
```

## Server Environment Variables

### Development/Production (.env)

```bash
# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration
DB_URL=mongodb+srv://username:<EMAIL>/homeexpert?retryWrites=true&w=majority

# Clerk Authentication (if using Clerk)
CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here

# Frontend URL for CORS (Production)
FRONTEND_URL=https://your-app-name.vercel.app

# JWT Secret (if using custom auth)
JWT_SECRET=your_jwt_secret_here

# Email Configuration (if needed)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# SMS/OTP Configuration (if needed)
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret

# File Upload Configuration (if using cloud storage)
CLOUDINARY_CLOUD_NAME=your_cloudinary_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Payment Gateway (if needed)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Google Maps API (if needed)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

## How to Use

### 1. Development Setup

1. Copy the environment variables to your `.env` files:
   - `client/.env` for client variables
   - `server/.env` for server variables

2. Update the values according to your setup:
   - Replace database URLs with your MongoDB connection string
   - Add your API keys for external services
   - Update URLs to match your local setup

3. Start the development servers:
   ```bash
   # Start server
   cd server && npm run dev
   
   # Start client (in another terminal)
   cd client && npm run dev
   ```

### 2. Testing the Configuration

1. Open your browser and go to `http://localhost:5173`
2. Look for a gear icon (⚙️) in the bottom-right corner
3. Click it to see the development configuration panel
4. Verify all environment variables are loaded correctly
5. Use the "Test API Connection" button to verify server connectivity

### 3. Production Deployment

For Vercel deployment, set these environment variables in your Vercel dashboard:

**Server Variables:**
- `DB_URL`
- `NODE_ENV=production`
- `FRONTEND_URL`
- `CLERK_SECRET_KEY` (if using Clerk)
- Any other API keys you're using

**Client Variables:**
- `VITE_API_BASE_URL`
- `VITE_CLERK_PUBLISHABLE_KEY` (if using Clerk)
- `VITE_GOOGLE_MAPS_API_KEY` (if needed)

## Troubleshooting

### Common Issues

1. **Environment variables not loading:**
   - Make sure variables are prefixed with `VITE_` for client-side
   - Restart the development server after changing .env files

2. **API connection issues:**
   - Check that `VITE_API_BASE_URL` matches your server URL
   - Verify CORS settings in server configuration

3. **Socket connection issues:**
   - Ensure `VITE_SOCKET_URL` is set correctly
   - Check that Socket.IO is enabled in development

### Debug Mode

Enable debug mode by setting `VITE_DEBUG=true` in your client `.env` file. This will:
- Show detailed logging in the browser console
- Display the configuration panel in development
- Log all API calls and responses

## Security Notes

- Never commit `.env` files to version control
- Use different API keys for development and production
- Keep sensitive keys (like database passwords) secure
- Use environment-specific configurations for different stages
