// Environment configuration for the client application

const config = {
  // API Base URL - automatically switches between development and production
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 
    (import.meta.env.MODE === 'production' 
      ? 'https://your-app-name.vercel.app/api' 
      : 'http://localhost:3000'),
  
  // Socket.IO URL
  SOCKET_URL: import.meta.env.VITE_SOCKET_URL || 
    (import.meta.env.MODE === 'production' 
      ? 'https://your-app-name.vercel.app' 
      : 'http://localhost:3000'),
  
  // Environment
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  MODE: import.meta.env.MODE || 'development',
  
  // Database URL (for reference)
  DB_URL: import.meta.env.VITE_DB_URL,
  
  // Clerk Authentication
  CLERK_PUBLISHABLE_KEY: import.meta.env.VITE_CLERK_PUBLISHABLE_KEY,
  
  // Google Maps API Key
  GOOGLE_MAPS_API_KEY: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
  
  // App Configuration
  APP_NAME: import.meta.env.VITE_APP_NAME || 'HomeXpert',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // Development flags
  IS_DEVELOPMENT: import.meta.env.MODE === 'development',
  IS_PRODUCTION: import.meta.env.MODE === 'production',
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  ENABLE_LOGGING: import.meta.env.VITE_ENABLE_LOGGING === 'true',
  
  // API Endpoints
  API_ENDPOINTS: {
    USER: import.meta.env.VITE_USER_API || '/user-api',
    SHOPKEEPER: import.meta.env.VITE_SHOPKEEPER_API || '/shopkeeper-api',
    WORKER: import.meta.env.VITE_WORKER_API || '/worker-api',
    DELIVERY: import.meta.env.VITE_DELIVERY_API || '/delivery-api',
    SHOPITEMS: import.meta.env.VITE_SHOPITEMS_API || '/shopitems-api',
    SHOPGOODS: import.meta.env.VITE_SHOPGOODS_API || '/shopgoods-api',
    VENDOR: import.meta.env.VITE_VENDOR_API || '/vendor-api',
    WORKS: import.meta.env.VITE_WORKS_API || '/works-api',
    ORDER: import.meta.env.VITE_ORDER_API || '/order-api',
    ADDRESS: import.meta.env.VITE_ADDRESS_API || '/address-api',
    FAVORITE: import.meta.env.VITE_FAVORITE_API || '/favorite-api',
    CART: import.meta.env.VITE_CART_API || '/cart-api',
  },
  
  // Feature Flags
  FEATURES: {
    ENABLE_SOCKET: import.meta.env.VITE_ENABLE_SOCKET === 'true',
    ENABLE_NOTIFICATIONS: import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true',
    ENABLE_LOCATION_TRACKING: import.meta.env.VITE_ENABLE_LOCATION_TRACKING === 'true',
  },
};

// Helper functions for API URLs
config.getApiUrl = (endpoint) => {
  return `${config.API_BASE_URL}${config.API_ENDPOINTS[endpoint.toUpperCase()]}`;
};

// Log configuration in development
if (config.IS_DEVELOPMENT && config.ENABLE_LOGGING) {
  console.log('🔧 Environment Configuration:', {
    API_BASE_URL: config.API_BASE_URL,
    SOCKET_URL: config.SOCKET_URL,
    MODE: config.MODE,
    NODE_ENV: config.NODE_ENV,
    DEBUG: config.DEBUG,
    FEATURES: config.FEATURES,
    API_ENDPOINTS: config.API_ENDPOINTS,
  });
}

export default config;
