import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';

const AdminProductManagement = () => {
  const [products, setProducts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  // New/Edit product form state
  const [productForm, setProductForm] = useState({
    name: '',
    category: '',
    imageUrl: '',
    description: '',
    price: '',
    quantity: '0'
  });

  // Fetch products when component mounts
  useEffect(() => {
    fetchProducts();
  }, []);

  // Fetch products from shopgoods and shopitems
  const fetchProducts = async () => {
    try {
      setLoading(true);

      console.log('Fetching products from shopgoods and shopitems');

      let allProducts = [];

      // Fetch shop goods
      try {
        const shopGoodsResponse = await axios.get('http://localhost:3000/shopgoods-api/shopGoods');

        if (shopGoodsResponse.data && shopGoodsResponse.data.payload) {
          console.log('Shop goods loaded:', shopGoodsResponse.data.payload);

          // Transform shop goods to match our product format
          const shopGoods = shopGoodsResponse.data.payload.map(item => ({
            _id: item._id,
            name: item.name,
            category: item.category || 'uncategorized',
            imageUrl: item.imageUrl,
            description: item.description,
            quantity: item.quantity,
            price: item.cost, // Note: shop goods use 'cost' instead of 'price'
            productType: 'shopGood'
          }));

          allProducts = [...allProducts, ...shopGoods];
        }
      } catch (shopGoodsError) {
        console.error('Error fetching shop goods:', shopGoodsError);
      }

      // Fetch shop items
      try {
        const shopItemsResponse = await axios.get('http://localhost:3000/shopitems-api/shopitems');

        if (shopItemsResponse.data && shopItemsResponse.data.payload) {
          console.log('Shop items loaded:', shopItemsResponse.data.payload);

          // Transform shop items to match our product format
          const shopItems = shopItemsResponse.data.payload.map(item => ({
            _id: item._id,
            name: item.name,
            category: item.category || 'uncategorized',
            imageUrl: item.imageUrl,
            description: item.description,
            quantity: item.quantity,
            price: item.cost, // Note: shop items use 'cost' instead of 'price'
            productType: 'shopItem'
          }));

          allProducts = [...allProducts, ...shopItems];
        }
      } catch (shopItemsError) {
        console.error('Error fetching shop items:', shopItemsError);
      }

      if (allProducts.length > 0) {
        setProducts(allProducts);
        setSearchResults(allProducts);
        toast.success(`Loaded ${allProducts.length} products successfully`);
      } else {
        // Fallback to mock data if no products were loaded
        console.log('No products found, using mock data as fallback');

        // For demo purposes, set some mock products
        const mockProducts = [
          {
            _id: '1',
            name: ['Tomato', 'Tamatar'],
            category: 'vegetables',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/tomato.jpg',
            description: 'Fresh red tomatoes',
            quantity: 50,
            price: 40,
            productType: 'shopItem'
          },
          {
            _id: '2',
            name: ['Onion', 'Pyaaz'],
            category: 'vegetables',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/onion.jpg',
            description: 'Fresh red onions',
            quantity: 0,
            price: 30,
            productType: 'shopItem'
          },
          {
            _id: '3',
            name: ['Rice', 'Chawal'],
            category: 'grains',
            imageUrl: 'https://res.cloudinary.com/dm22f11c0/image/upload/v1742713064/rice.jpg',
            description: 'Premium basmati rice',
            quantity: 30,
            price: 60,
            productType: 'shopGood'
          }
        ];

        setProducts(mockProducts);
        setSearchResults(mockProducts);
        toast.info('Using demo products (API unavailable)');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults(products);
      return;
    }

    const query = searchQuery.toLowerCase();
    const results = products.filter(product => {
      // Check if query matches any name in the name array
      const nameMatch = product.name.some(name =>
        name.toLowerCase().includes(query)
      );

      // Check if query matches category
      const categoryMatch = product.category.toLowerCase().includes(query);

      // Check if query matches description
      const descriptionMatch = product.description.toLowerCase().includes(query);

      return nameMatch || categoryMatch || descriptionMatch;
    });

    setSearchResults(results);
  };

  // Handle form change
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setProductForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle edit product
  const handleEditProduct = (product) => {
    setIsEditing(true);
    setSelectedProduct(product);

    // Set form values
    setProductForm({
      name: product.name.join(', '),
      category: product.category,
      imageUrl: product.imageUrl,
      description: product.description,
      price: product.price.toString(),
      quantity: product.quantity.toString()
    });
  };

  // Handle add new product
  const handleAddNewProduct = () => {
    setIsEditing(false);
    setSelectedProduct(null);

    // Reset form
    setProductForm({
      name: '',
      category: '',
      imageUrl: '',
      description: '',
      price: '',
      quantity: '0'
    });
  };

  // Handle delete product
  const handleDeleteProduct = async (productId, productType) => {
    if (!window.confirm('Are you sure you want to delete this product?')) {
      return;
    }

    try {
      setLoading(true);

      console.log(`Deleting product ${productId} of type ${productType}`);

      // Determine which API endpoint to use based on product type
      const apiEndpoint = productType === 'shopGood'
        ? 'shopgoods-api'
        : 'shopitems-api';

      const endpointPath = productType === 'shopGood'
        ? 'shopgood'
        : 'shopitem';

      try {
        // Call the API to delete the product
        const response = await axios.delete(`http://localhost:3000/${apiEndpoint}/${endpointPath}/${productId}`);

        if (response.data && response.data.message) {
          console.log('API response:', response.data);

          // Update the local state
          const updatedProducts = products.filter(product => product._id !== productId);
          setProducts(updatedProducts);
          setSearchResults(updatedProducts);
          toast.success('Product deleted successfully');
        }
      } catch (apiError) {
        console.error('API error:', apiError);

        // For demo purposes, update the local state even if API fails
        const updatedProducts = products.filter(product => product._id !== productId);
        setProducts(updatedProducts);
        setSearchResults(updatedProducts);
        toast.info('Product deleted (demo mode)');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmitProduct = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Validate form
      if (!productForm.name || !productForm.category || !productForm.imageUrl ||
          !productForm.description || !productForm.price) {
        toast.error('Please fill all required fields');
        return;
      }

      // Determine product type based on category
      // For this example, we'll consider 'vegetables', 'fruits', 'dairy' as shopItems
      // and everything else as shopGoods
      const isShopItem = ['vegetables', 'fruits', 'dairy', 'bakery'].includes(productForm.category.toLowerCase());
      const productType = isShopItem ? 'shopItem' : 'shopGood';

      // Determine which API endpoint to use based on product type
      const apiEndpoint = productType === 'shopGood'
        ? 'shopgoods-api'
        : 'shopitems-api';

      const endpointPath = productType === 'shopGood'
        ? 'shopgood'
        : 'shopitem';

      // Prepare product data
      const productData = {
        name: productForm.name.split(',').map(n => n.trim()),
        category: productForm.category,
        imageUrl: productForm.imageUrl,
        description: productForm.description,
        cost: parseFloat(productForm.price), // Note: API uses 'cost' instead of 'price'
        quantity: parseInt(productForm.quantity, 10)
      };

      if (isEditing && selectedProduct) {
        // Update existing product
        console.log(`Updating ${selectedProduct.productType} ${selectedProduct._id}`, productData);

        // Use the product's existing type for the API endpoint
        const updateApiEndpoint = selectedProduct.productType === 'shopGood'
          ? 'shopgoods-api'
          : 'shopitems-api';

        const updateEndpointPath = selectedProduct.productType === 'shopGood'
          ? 'shopgood'
          : 'shopitem';

        try {
          // If only the quantity changed, use the update-quantity endpoint
          if (Object.keys(productData).length === 1 && 'quantity' in productData) {
            const response = await axios.put(`http://localhost:3000/${updateApiEndpoint}/update-quantity/${selectedProduct._id}`, {
              quantity: productData.quantity
            });

            if (response.data && response.data.payload) {
              console.log('Quantity update API response:', response.data);

              // Transform the response to match our product format
              const updatedProduct = {
                ...selectedProduct,
                quantity: response.data.payload.quantity
              };

              // Update the local state
              const updatedProducts = products.map(product => {
                if (product._id === selectedProduct._id) {
                  return updatedProduct;
                }
                return product;
              });

              setProducts(updatedProducts);
              setSearchResults(updatedProducts);
              toast.success('Product quantity updated successfully');

              // Refresh products after a short delay
              setTimeout(() => {
                fetchProducts();
              }, 1000);
            }
          } else {
            // Use the general update endpoint for full product updates
            const response = await axios.put(`http://localhost:3000/${updateApiEndpoint}/${updateEndpointPath}/${selectedProduct._id}`, productData);

            if (response.data && response.data.payload) {
              console.log('API response:', response.data);

              // Transform the response to match our product format
              const updatedProduct = {
                _id: response.data.payload._id,
                name: response.data.payload.name,
                category: response.data.payload.category,
                imageUrl: response.data.payload.imageUrl,
                description: response.data.payload.description,
                quantity: response.data.payload.quantity,
                price: response.data.payload.cost, // Note: API returns 'cost'
                productType: selectedProduct.productType
              };

              // Update the local state
              const updatedProducts = products.map(product => {
                if (product._id === selectedProduct._id) {
                  return updatedProduct;
                }
                return product;
              });

              setProducts(updatedProducts);
              setSearchResults(updatedProducts);
              toast.success('Product updated successfully');

              // Refresh products after a short delay
              setTimeout(() => {
                fetchProducts();
              }, 1000);
            }
          }
        } catch (apiError) {
          console.error('API error:', apiError);
          toast.error('Failed to update product in database');
        }
      } else {
        // Add new product
        console.log(`Adding new ${productType}`, productData);

        try {
          const response = await axios.post(`http://localhost:3000/${apiEndpoint}/${endpointPath}`, productData);

          if (response.data && response.data.payload) {
            console.log('API response:', response.data);

            // Transform the response to match our product format
            const newProduct = {
              _id: response.data.payload._id,
              name: response.data.payload.name,
              category: response.data.payload.category,
              imageUrl: response.data.payload.imageUrl,
              description: response.data.payload.description,
              quantity: response.data.payload.quantity,
              price: response.data.payload.cost, // Note: API returns 'cost'
              productType: productType
            };

            setProducts([...products, newProduct]);
            setSearchResults([...products, newProduct]);
            toast.success('Product added successfully');

            // Refresh products after a short delay
            setTimeout(() => {
              fetchProducts();
            }, 1000);
          }
        } catch (apiError) {
          console.error('API error:', apiError);
          toast.error('Failed to add product to database');
        }
      }

      // Reset form and state
      setProductForm({
        name: '',
        category: '',
        imageUrl: '',
        description: '',
        price: '',
        quantity: '0'
      });

      setIsEditing(false);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Error submitting product:', error);
      toast.error('Failed to submit product');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Product Management</h2>
          <p className="text-gray-600">Add, edit, and manage products</p>
        </div>

        <button
          onClick={handleAddNewProduct}
          className="px-4 py-2 bg-primary-custom text-white rounded-md hover:bg-opacity-90 flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Add New Product
        </button>
      </div>

      {/* Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-blue-700">Total Products</h3>
          <p className="text-2xl font-bold">{products.length}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-green-700">In Stock</h3>
          <p className="text-2xl font-bold">{products.filter(p => p.quantity > 0).length}</p>
        </div>
        <div className="bg-red-50 p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-red-700">Out of Stock</h3>
          <p className="text-2xl font-bold">{products.filter(p => p.quantity <= 0).length}</p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-purple-700">Categories</h3>
          <p className="text-2xl font-bold">{new Set(products.map(p => p.category)).size}</p>
        </div>
      </div>

      {/* Product Form */}
      {(isEditing || selectedProduct === null) && (
        <div className="mb-8">
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h3 className="text-lg font-semibold text-blue-700 mb-2">
              {isEditing ? 'Edit Product' : 'Add New Product'}
            </h3>
            <p className="text-gray-700">
              {isEditing
                ? 'Update the product details below'
                : 'Fill in the details below to add a new product'}
            </p>
          </div>

          <form onSubmit={handleSubmitProduct} className="bg-white rounded-lg border p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-gray-700 font-medium mb-2">Product Name(s)*</label>
                <input
                  type="text"
                  name="name"
                  placeholder="Name, Alternate names (comma separated)"
                  className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
                  value={productForm.name}
                  onChange={handleFormChange}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Enter multiple names separated by commas (e.g., "Tomato, Tamatar")</p>
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">Category*</label>
                <select
                  name="category"
                  className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
                  value={productForm.category}
                  onChange={handleFormChange}
                  required
                >
                  <option value="">Select a category</option>
                  <option value="vegetables">Vegetables</option>
                  <option value="fruits">Fruits</option>
                  <option value="dairy">Dairy</option>
                  <option value="bakery">Bakery</option>
                  <option value="beverages">Beverages</option>
                  <option value="snacks">Snacks</option>
                  <option value="household">Household</option>
                  <option value="personal_care">Personal Care</option>
                </select>
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">Price (₹)*</label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">₹</span>
                  <input
                    type="number"
                    name="price"
                    min="0"
                    step="0.01"
                    placeholder="Price in rupees"
                    className="w-full p-3 pl-8 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
                    value={productForm.price}
                    onChange={handleFormChange}
                    required
                  />
                </div>
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">Quantity*</label>
                <input
                  type="number"
                  name="quantity"
                  min="0"
                  placeholder="Initial stock quantity"
                  className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
                  value={productForm.quantity}
                  onChange={handleFormChange}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Enter 0 if the product is out of stock</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-gray-700 font-medium mb-2">Image URL*</label>
                <input
                  type="url"
                  name="imageUrl"
                  placeholder="https://example.com/image.jpg"
                  className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
                  value={productForm.imageUrl}
                  onChange={handleFormChange}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Enter a valid URL for the product image</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-gray-700 font-medium mb-2">Description*</label>
                <textarea
                  name="description"
                  rows="4"
                  placeholder="Product description"
                  className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
                  value={productForm.description}
                  onChange={handleFormChange}
                  required
                ></textarea>
              </div>
            </div>

            <div className="border-t pt-6 flex justify-end">
              <button
                type="button"
                className="px-6 py-3 mr-3 border border-gray-300 rounded-md hover:bg-gray-100"
                onClick={() => {
                  if (isEditing) {
                    setIsEditing(false);
                    setSelectedProduct(null);
                  }

                  setProductForm({
                    name: '',
                    category: '',
                    imageUrl: '',
                    description: '',
                    price: '',
                    quantity: '0'
                  });
                }}
              >
                {isEditing ? 'Cancel' : 'Reset Form'}
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-primary-custom text-white rounded-md hover:bg-opacity-90 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isEditing ? 'Updating...' : 'Adding...'}
                  </>
                ) : (
                  isEditing ? 'Update Product' : 'Add Product'
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Search Bar */}
      <div className="flex mb-6">
        <input
          type="text"
          placeholder="Search products by name, category or description..."
          className="flex-1 p-2 border rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary-custom"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
        />
        <button
          className="bg-primary-custom text-white px-4 py-2 rounded-r-md hover:bg-opacity-90"
          onClick={handleSearch}
        >
          Search
        </button>
      </div>

      {/* Products Grid */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <h3 className="text-lg font-semibold text-gray-800">Products ({searchResults.length})</h3>
          <div className="flex flex-wrap gap-3">
            <select
              className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
              onChange={(e) => {
                if (e.target.value === 'all') {
                  setSearchResults(products);
                } else {
                  setSearchResults(products.filter(p => p.category === e.target.value));
                }
              }}
            >
              <option value="all">All Categories</option>
              {Array.from(new Set(products.map(p => p.category))).map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            <select
              className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
              onChange={(e) => {
                if (e.target.value === 'all') {
                  setSearchResults(products);
                } else {
                  setSearchResults(products.filter(p => p.productType === e.target.value));
                }
              }}
            >
              <option value="all">All Types</option>
              <option value="shopGood">Shop Goods</option>
              <option value="shopItem">Shop Items</option>
            </select>

            <select
              className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-custom"
              onChange={(e) => {
                const value = e.target.value;
                let sortedProducts = [...searchResults];

                if (value === 'name-asc') {
                  sortedProducts.sort((a, b) => {
                    const nameA = a.name && a.name.length > 0 ? a.name[0] : '';
                    const nameB = b.name && b.name.length > 0 ? b.name[0] : '';
                    return nameA.localeCompare(nameB);
                  });
                } else if (value === 'name-desc') {
                  sortedProducts.sort((a, b) => {
                    const nameA = a.name && a.name.length > 0 ? a.name[0] : '';
                    const nameB = b.name && b.name.length > 0 ? b.name[0] : '';
                    return nameB.localeCompare(nameA);
                  });
                } else if (value === 'price-asc') {
                  sortedProducts.sort((a, b) => a.price - b.price);
                } else if (value === 'price-desc') {
                  sortedProducts.sort((a, b) => b.price - a.price);
                } else if (value === 'quantity-asc') {
                  sortedProducts.sort((a, b) => a.quantity - b.quantity);
                } else if (value === 'quantity-desc') {
                  sortedProducts.sort((a, b) => b.quantity - a.quantity);
                }

                setSearchResults(sortedProducts);
              }}
            >
              <option value="">Sort By</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="price-asc">Price (Low to High)</option>
              <option value="price-desc">Price (High to Low)</option>
              <option value="quantity-asc">Quantity (Low to High)</option>
              <option value="quantity-desc">Quantity (High to Low)</option>
            </select>
          </div>
        </div>

        {loading && searchResults.length === 0 ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-custom"></div>
            <p className="ml-3 text-gray-600">Loading products...</p>
          </div>
        ) : searchResults.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {searchResults.map(product => (
              <div key={product._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-48 overflow-hidden bg-gray-200">
                  <img
                    src={product.imageUrl}
                    alt={product.name && product.name.length > 0 ? product.name[0] : 'Product Image'}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';
                    }}
                  />
                  <div className="absolute top-2 right-2 flex flex-col gap-1">
                    <span className={`px-2 py-1 ${product.productType === 'shopGood' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'} rounded-full text-xs font-medium`}>
                      {product.productType ? (product.productType === 'shopGood' ? 'Shop Good' : 'Shop Item') : 'Unknown Type'}
                    </span>
                    {product.quantity > 0 ? (
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                        In Stock
                      </span>
                    ) : (
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                        Out of Stock
                      </span>
                    )}
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                    <span className="text-white text-xs font-medium uppercase tracking-wider">
                      {product.category}
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-800 line-clamp-1">
                      {product.name && product.name.length > 0 ? product.name[0] : 'Unnamed Product'}
                    </h3>
                    <span className="text-lg font-bold text-primary-custom">₹{product.price}</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {product.description ? product.description : 'No description available'}
                  </p>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                      <span className="font-medium">Qty:</span> {product.quantity}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        className="px-3 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                        onClick={() => handleEditProduct(product)}
                      >
                        Edit
                      </button>
                      <button
                        className="px-3 py-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                        onClick={() => handleDeleteProduct(product._id, product.productType)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminProductManagement;
