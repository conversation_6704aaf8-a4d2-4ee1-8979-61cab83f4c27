// API utility functions using environment configuration
import config from '../config/environment.js';

// Helper function to build API URLs
export const buildApiUrl = (endpoint, path = '') => {
  const baseUrl = config.API_BASE_URL;
  const apiEndpoint = config.API_ENDPOINTS[endpoint.toUpperCase()];
  
  if (!apiEndpoint) {
    console.warn(`Unknown API endpoint: ${endpoint}`);
    return `${baseUrl}/${endpoint}${path}`;
  }
  
  return `${baseUrl}${apiEndpoint}${path}`;
};

// Pre-built API URLs for common endpoints
export const API_URLS = {
  // User APIs
  USER_REGISTER: buildApiUrl('USER', '/register'),
  USER_LOGIN: buildApiUrl('USER', '/login'),
  USER_PROFILE: buildApiUrl('USER', '/profile'),
  
  // Shopkeeper APIs
  SHOPKEEPER_REGISTER: buildApiUrl('SHOPKEEPER', '/register'),
  SHOPKEEPER_LOGIN: buildApiUrl('SHOPKEEPER', '/login'),
  SHOPKEEPER_PROFILE: buildApiUrl('SHOPKEEPER', '/profile'),
  
  // Delivery APIs
  DELIVERY_REGISTER: buildApiUrl('DELIVERY', '/deliveryPerson'),
  DELIVERY_LOGIN: buildApiUrl('DELIVERY', '/login'),
  DELIVERY_PROFILE: buildApiUrl('DELIVERY', '/profile'),
  
  // Shop Items APIs
  SHOP_ITEMS: buildApiUrl('SHOPITEMS', '/shopitems'),
  SHOP_ITEMS_BY_CATEGORY: (category) => buildApiUrl('SHOPITEMS', `/shopitems/category/${category}`),
  
  // Shop Goods APIs
  SHOP_GOODS: buildApiUrl('SHOPGOODS', '/shopGoods'),
  SHOP_GOODS_BY_CATEGORY: (category) => buildApiUrl('SHOPGOODS', `/shopGoods/category/${category}`),
  
  // Order APIs
  ORDERS: buildApiUrl('ORDER', '/order'),
  ORDER_BY_ID: (id) => buildApiUrl('ORDER', `/order/${id}`),
  ORDER_STATUS: (id) => buildApiUrl('ORDER', `/order/${id}/status`),
  ORDER_ACCEPT: (id) => buildApiUrl('ORDER', `/order/${id}/accept`),
  ORDERS_DELIVERY: (deliveryPersonId) => buildApiUrl('ORDER', `/orders/delivery/${deliveryPersonId}`),
  ORDERS_AVAILABLE: buildApiUrl('ORDER', '/orders/available'),
  ORDERS_COMPLETED: (deliveryPersonId) => buildApiUrl('ORDER', `/orders/delivery/${deliveryPersonId}/completed`),
  ORDERS_STATS: (deliveryPersonId) => buildApiUrl('ORDER', `/orders/delivery/${deliveryPersonId}/stats`),
  
  // Cart APIs
  CART: buildApiUrl('CART', '/cart'),
  CART_ADD: buildApiUrl('CART', '/cart/add'),
  CART_REMOVE: buildApiUrl('CART', '/cart/remove'),
  CART_CLEAR: buildApiUrl('CART', '/cart/clear'),
  
  // Address APIs
  ADDRESSES: buildApiUrl('ADDRESS', '/addresses'),
  ADDRESS_BY_ID: (id) => buildApiUrl('ADDRESS', `/address/${id}`),
  
  // Favorite APIs
  FAVORITES: buildApiUrl('FAVORITE', '/favorites'),
  FAVORITE_ADD: buildApiUrl('FAVORITE', '/favorite/add'),
  FAVORITE_REMOVE: buildApiUrl('FAVORITE', '/favorite/remove'),
};

// Logging utility for API calls
export const logApiCall = (method, url, data = null) => {
  if (config.DEBUG && config.ENABLE_LOGGING) {
    console.log(`🌐 API ${method.toUpperCase()}: ${url}`, data ? { data } : '');
  }
};

// Error handling utility
export const handleApiError = (error, context = '') => {
  if (config.DEBUG && config.ENABLE_LOGGING) {
    console.error(`❌ API Error ${context}:`, error);
  }
  
  if (error.response) {
    // Server responded with error status
    return {
      message: error.response.data?.message || 'Server error occurred',
      status: error.response.status,
      data: error.response.data
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'No response from server. Please check your connection.',
      status: 0,
      data: null
    };
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: -1,
      data: null
    };
  }
};

export default {
  buildApiUrl,
  API_URLS,
  logApiCall,
  handleApiError
};
