import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import axios from 'axios';
import AdminSidebar from './AdminSidebar';
import DeliveryPartnersList from './DeliveryPartnersList';
import DashboardOverview from './DashboardOverview';
import OrdersManagement from './OrdersManagement';
import AdminProductManagement from './AdminProductManagement';
import ShopkeepersList from './ShopkeepersList';
import SettingsPanel from './SettingsPanel';

// API base URL
const API_BASE_URL = 'http://localhost:3000';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [stats, setStats] = useState({
    totalPartners: 0,
    pendingApprovals: 0,
    activePartners: 0,
    inactivePartners: 0,
    totalOrders: 0,
    pendingOrders: 0,
    totalProducts: 0,
    totalShopkeepers: 0,
    totalRevenue: 0
  });

  // Check if user is admin
  useEffect(() => {
    const adminAuth = localStorage.getItem('adminAuth');

    if (!adminAuth) {
      navigate('/admin/login');
      return;
    }

    try {
      const parsedAuth = JSON.parse(adminAuth);

      if (!parsedAuth.isAdmin) {
        navigate('/admin/login');
        return;
      }

      setIsAdmin(true);

      // Load all dashboard data
      loadDashboardData();
    } catch (error) {
      console.error('Admin auth error:', error);
      navigate('/admin/login');
    }
  }, [navigate]);

  // Load all dashboard data
  const loadDashboardData = async () => {
    try {
      // Fetch delivery partners from the API
      const response = await axios.get(`${API_BASE_URL}/delivery-api/deliveryPerson`);

      if (response.data && response.data.payload) {
        const partners = response.data.payload;

        // Update stats based on real data
        const updatedStats = {
          totalPartners: partners.length,
          pendingApprovals: partners.filter(p => p.status === 'PENDING').length,
          activePartners: partners.filter(p => p.status === 'ACTIVE').length,
          inactivePartners: partners.filter(p => p.status === 'INACTIVE').length,
          totalOrders: 0, // Will be implemented when order system is ready
          pendingOrders: 0,
          totalProducts: 0, // Will be implemented when product system is ready
          totalShopkeepers: 0, // Will be implemented when shopkeeper system is ready
          totalRevenue: 0 // Will be implemented when order system is ready
        };

        setStats(updatedStats);

        // Store partners in localStorage for other components to use
        localStorage.setItem('deliveryPartners', JSON.stringify(partners));
      }

      setLoading(false);
    } catch (error) {
      console.error('Load dashboard data error:', error);
      toast.error('Failed to load dashboard data');
      setLoading(false);
    }
  };

  // Update statistics
  const updateStats = (partners) => {
    const stats = {
      totalPartners: partners.length,
      pendingApprovals: partners.filter(p => p.status === 'PENDING').length,
      activePartners: partners.filter(p => p.status === 'ACTIVE').length,
      inactivePartners: partners.filter(p => p.status === 'INACTIVE').length
    };

    setStats(stats);
  };

  // Handle partner status update
  const handleStatusUpdate = async (partnerId, newStatus) => {
    try {
      setLoading(true);

      // Make an API call to update the partner's status
      const response = await axios.put(`${API_BASE_URL}/delivery-api/deliveryPerson/${partnerId}`, {
        status: newStatus
      });

      if (response.data && response.data.payload) {
        // Reload dashboard data to get updated stats
        await loadDashboardData();

        toast.success(`Partner status updated to ${newStatus}`);
      } else {
        throw new Error('Failed to update partner status');
      }
    } catch (error) {
      console.error('Update partner status error:', error);
      toast.error(error.response?.data?.message || 'Failed to update partner status');
    } finally {
      setLoading(false);
    }
  };

  // Handle admin logout
  const handleLogout = () => {
    localStorage.removeItem('adminAuth');
    navigate('/admin/login');
    toast.success('Logged out successfully');
  };

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8a4af3]"></div>
          <p className="mt-4 text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div>
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
              <p className="text-gray-600">Welcome to the HomeXpert admin dashboard</p>
            </div>

            {/* Stats cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-[#8a4af3]">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-[#8a4af3]/10 text-[#8a4af3]">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-sm font-medium text-gray-600">Total Partners</h2>
                    <p className="text-2xl font-semibold text-gray-900">{stats.totalPartners}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-sm font-medium text-gray-600">Pending Approvals</h2>
                    <p className="text-2xl font-semibold text-gray-900">{stats.pendingApprovals}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-sm font-medium text-gray-600">Total Orders</h2>
                    <p className="text-2xl font-semibold text-gray-900">{stats.totalOrders}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-green-100 text-green-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-sm font-medium text-gray-600">Total Revenue</h2>
                    <p className="text-2xl font-semibold text-gray-900">₹{stats.totalRevenue.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick actions */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => setActiveTab('pending')}
                  className="flex items-center justify-center p-4 bg-[#8a4af3]/10 text-[#8a4af3] rounded-lg hover:bg-[#8a4af3]/20 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Approve Delivery Partners
                </button>
                <button
                  onClick={() => setActiveTab('orders')}
                  className="flex items-center justify-center p-4 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  Manage Orders
                </button>
                <button
                  onClick={() => setActiveTab('products')}
                  className="flex items-center justify-center p-4 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  Manage Products
                </button>
              </div>
            </div>

            {/* Recent activity */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
              <div className="flex flex-col items-center justify-center py-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-gray-500 text-sm">No recent activity to display</p>
                <p className="text-gray-400 text-xs mt-1">Activity will appear here as it happens</p>
              </div>
            </div>
          </div>
        );
      case 'pending':
      case 'active':
      case 'inactive':
      case 'all':
        return <DeliveryPartnersList activeTab={activeTab} handleStatusUpdate={handleStatusUpdate} />;
      case 'orders':
        return <div className="p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Orders Management</h2>
          <p className="text-gray-600 mb-4">This feature will be implemented soon.</p>
        </div>;
      case 'products':
        return <AdminProductManagement />;
      case 'shopkeepers':
        return <div className="p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Shopkeepers Management</h2>
          <p className="text-gray-600 mb-4">This feature will be implemented soon.</p>
        </div>;
      case 'settings':
        return <div className="p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Settings</h2>
          <p className="text-gray-600 mb-4">This feature will be implemented soon.</p>
        </div>;
      default:
        return <div>Page not found</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex">
        {/* Sidebar */}
        <AdminSidebar activeTab={activeTab} setActiveTab={setActiveTab} handleLogout={handleLogout} />

        {/* Main content */}
        <div className="flex-1 p-6 overflow-y-auto mt-16">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
