import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useDeliveryAuth } from '../../../context/DeliveryAuthContext';
import { startLocationSharing, stopLocationSharing, updateOrderStatus } from '../../../services/socketService';

// API base URL
const API_BASE_URL = 'http://localhost:3000';

const OrdersList = ({ orders, newOrderNotification, onDismissNotification }) => {
  const { currentDeliveryPartner } = useDeliveryAuth();
  const [expandedOrderId, setExpandedOrderId] = useState(null);
  const [activeOrders, setActiveOrders] = useState(orders);
  const [loading, setLoading] = useState(false);
  const [locationSharingOrders, setLocationSharingOrders] = useState({});
  const audioRef = useRef(null);

  // Update active orders when orders prop changes
  useEffect(() => {
    setActiveOrders(orders);

    // Play notification sound when a new order notification is received
    if (newOrderNotification && audioRef.current) {
      audioRef.current.play().catch(e => console.error('Error playing sound:', e));
    }
  }, [orders, newOrderNotification]);

  // Cleanup location sharing when component unmounts
  useEffect(() => {
    return () => {
      // Stop all location sharing
      Object.entries(locationSharingOrders).forEach(([orderId, locationIds]) => {
        console.log('Cleaning up location sharing for order:', orderId);
        stopLocationSharing(locationIds);
      });
    };
  }, [locationSharingOrders]);

  // Toggle order details
  const toggleOrderDetails = (orderId) => {
    setExpandedOrderId(expandedOrderId === orderId ? null : orderId);
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { hour: '2-digit', minute: '2-digit', hour12: true };
    return new Date(dateString).toLocaleTimeString(undefined, options);
  };

  // Start location sharing for an order
  const startSharingLocation = (orderId) => {
    if (locationSharingOrders[orderId]) {
      console.log('Already sharing location for this order');
      return;
    }

    if (!currentDeliveryPartner || !currentDeliveryPartner._id) {
      console.error('No delivery partner ID found');
      return;
    }

    console.log('Starting location sharing for order:', orderId);

    // Start sharing location
    const locationIds = startLocationSharing(currentDeliveryPartner._id, orderId);

    // Store the location sharing IDs
    setLocationSharingOrders(prev => ({
      ...prev,
      [orderId]: locationIds
    }));

    toast.info('Location sharing started for this order');
  };

  // Stop location sharing for an order
  const stopSharingLocation = (orderId) => {
    if (!locationSharingOrders[orderId]) {
      console.log('Not sharing location for this order');
      return;
    }

    console.log('Stopping location sharing for order:', orderId);

    // Stop sharing location
    stopLocationSharing(locationSharingOrders[orderId]);

    // Remove the location sharing IDs
    setLocationSharingOrders(prev => {
      const newState = { ...prev };
      delete newState[orderId];
      return newState;
    });

    toast.info('Location sharing stopped for this order');
  };

  // Handle accept order
  const handleAcceptOrder = async (orderId) => {
    try {
      setLoading(true);

      // Call API to accept the order
      const response = await axios.post(
        `${API_BASE_URL}/order-api/order/${orderId}/accept`,
        { deliveryPersonId: currentDeliveryPartner._id }
      );

      if (response.data && response.data.payload) {
        // Update the order in the local state
        setActiveOrders(
          activeOrders.map((order) =>
            order.id === orderId ? { ...order, status: 'ASSIGNED' } : order
          )
        );

        // Clear notification if this was the notified order
        if (newOrderNotification && newOrderNotification.id === orderId && onDismissNotification) {
          onDismissNotification();
        }

        // Start location sharing for this order
        startSharingLocation(orderId);

        toast.success('Order accepted successfully!');
      }
    } catch (error) {
      console.error('Error accepting order:', error);
      toast.error(error.response?.data?.message || 'Failed to accept order');
    } finally {
      setLoading(false);
    }
  };

  // Handle complete order
  const handleCompleteOrder = async (orderId) => {
    try {
      setLoading(true);

      // Call API to complete the order
      const response = await axios.patch(
        `${API_BASE_URL}/order-api/order/${orderId}/status`,
        { orderStatus: 'DELIVERED' }
      );

      if (response.data && response.data.payload) {
        // Update the order in the local state
        setActiveOrders(
          activeOrders.map((order) =>
            order.id === orderId ? { ...order, status: 'DELIVERED' } : order
          )
        );

        // Update order status via socket
        updateOrderStatus(orderId, 'DELIVERED', currentDeliveryPartner._id);

        // Stop location sharing for this order
        stopSharingLocation(orderId);

        toast.success('Order marked as delivered!');

        // Refresh the orders list after a short delay
        setTimeout(() => {
          // Move the order to completed orders
          const orderToMove = activeOrders.find(order => order.id === orderId);
          if (orderToMove) {
            setActiveOrders(prevOrders =>
              prevOrders.filter(order => order.id !== orderId)
            );
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Error completing order:', error);

      // More detailed error handling
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const errorMessage = error.response.data?.message || 'Failed to update order status';
        toast.error(errorMessage);
        console.error('Server response:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        toast.error('No response from server. Please check your connection.');
        console.error('No response received:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        toast.error('Error setting up request. Please try again.');
        console.error('Request setup error:', error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // Dismiss notification
  const dismissNotification = () => {
    if (onDismissNotification) {
      onDismissNotification();
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'ASSIGNED':
        return 'bg-blue-100 text-blue-800';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter orders by status
  const pendingOrders = activeOrders.filter(order => order.status === 'PENDING');
  const assignedOrders = activeOrders.filter(order => order.status === 'ASSIGNED');
  const completedOrders = activeOrders.filter(order => order.status === 'DELIVERED');

  return (
    <div>
      {/* Audio element for notification sound */}
      <audio ref={audioRef} preload="auto">
        <source src="https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>

      {/* New Order Notification */}
      {newOrderNotification && (
        <div className="fixed inset-x-0 top-20 mx-auto max-w-md z-50 animate-bounce">
          <div className="bg-[#8a4af3] text-white p-4 rounded-lg shadow-lg mx-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                <div className="bg-white p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#8a4af3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-bold">New Order Available!</h3>
                  <p className="text-sm">From {newOrderNotification.customerName}</p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    toggleOrderDetails(newOrderNotification.id);
                    dismissNotification();
                  }}
                  className="bg-white text-[#8a4af3] px-3 py-1 rounded-md text-sm font-medium hover:bg-opacity-90"
                >
                  View
                </button>
                <button
                  onClick={dismissNotification}
                  className="bg-white/20 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-opacity-30"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pending Orders */}
      {pendingOrders.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">New Orders</h3>
          <div className="space-y-4">
            {pendingOrders.map((order) => (
              <div key={order.id} className="border rounded-lg overflow-hidden bg-white">
                <div
                  className="p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleOrderDetails(order.id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h4 className="font-medium">{order.id}</h4>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(order.status)}`}>
                          {order.status}
                        </span>
                        {locationSharingOrders[order.id] && (
                          <span className="ml-2 flex items-center text-xs text-green-600">
                            <span className="h-2 w-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                            Sharing location
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 text-sm mt-1">{order.customerName}</p>
                      <p className="text-gray-500 text-sm">{formatDate(order.createdAt)}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">₹{order.amount}</p>
                      <p className="text-gray-500 text-sm">{order.paymentMethod}</p>
                    </div>
                  </div>
                </div>

                {expandedOrderId === order.id && (
                  <div className="p-4 border-t bg-gray-50">
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Delivery Address</h5>
                      <p className="text-gray-600">{order.customerAddress}</p>
                    </div>
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Items</h5>
                      <ul className="space-y-1">
                        {order.items.map((item, index) => (
                          <li key={index} className="text-gray-600">
                            {item.name} x {item.quantity}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="flex justify-end">
                      <button
                        onClick={() => handleAcceptOrder(order.id)}
                        disabled={loading}
                        className={`px-4 py-2 bg-[#8a4af3] text-white rounded-md hover:bg-opacity-90 transition-colors ${
                          loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                      >
                        {loading ? (
                          <div className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </div>
                        ) : 'Accept Order'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Assigned Orders */}
      {assignedOrders.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Ongoing Deliveries</h3>
          <div className="space-y-4">
            {assignedOrders.map((order) => (
              <div key={order.id} className="border rounded-lg overflow-hidden bg-white">
                <div
                  className="p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleOrderDetails(order.id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h4 className="font-medium">{order.id}</h4>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm mt-1">{order.customerName}</p>
                      <p className="text-gray-500 text-sm">ETA: {order.estimatedDeliveryTime}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">₹{order.amount}</p>
                      <p className="text-gray-500 text-sm">{order.paymentMethod}</p>
                    </div>
                  </div>
                </div>

                {expandedOrderId === order.id && (
                  <div className="p-4 border-t bg-gray-50">
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Delivery Address</h5>
                      <p className="text-gray-600">{order.customerAddress}</p>
                    </div>
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Items</h5>
                      <ul className="space-y-1">
                        {order.items.map((item, index) => (
                          <li key={index} className="text-gray-600">
                            {item.name} x {item.quantity}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="flex justify-between">
                      <a
                        href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(order.customerAddress)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                      >
                        Navigate
                      </a>
                      <button
                        onClick={() => handleCompleteOrder(order.id)}
                        disabled={loading}
                        className={`px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors ${
                          loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                      >
                        {loading ? (
                          <div className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </div>
                        ) : 'Mark as Delivered'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Completed Orders */}
      {completedOrders.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4">Completed Deliveries</h3>
          <div className="space-y-4">
            {completedOrders.map((order) => (
              <div key={order.id} className="border rounded-lg overflow-hidden bg-white">
                <div
                  className="p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleOrderDetails(order.id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h4 className="font-medium">{order.id}</h4>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm mt-1">{order.customerName}</p>
                      <p className="text-gray-500 text-sm">{formatDate(order.createdAt)}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">₹{order.amount}</p>
                      <p className="text-gray-500 text-sm">{order.paymentMethod}</p>
                    </div>
                  </div>
                </div>

                {expandedOrderId === order.id && (
                  <div className="p-4 border-t bg-gray-50">
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Delivery Address</h5>
                      <p className="text-gray-600">{order.customerAddress}</p>
                    </div>
                    <div>
                      <h5 className="font-medium mb-2">Items</h5>
                      <ul className="space-y-1">
                        {order.items.map((item, index) => (
                          <li key={index} className="text-gray-600">
                            {item.name} x {item.quantity}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Orders */}
      {activeOrders.length === 0 && (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
          <p className="mt-1 text-sm text-gray-500">New orders will appear here when they are available.</p>
        </div>
      )}
    </div>
  );
};

export default OrdersList;
