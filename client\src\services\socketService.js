import { io } from 'socket.io-client';
import { toast } from 'react-toastify';

// API base URL
const API_BASE_URL = 'http://localhost:3000';

let socket;

// Initialize the socket connection
export const initializeSocket = () => {
  if (!socket) {
    socket = io(API_BASE_URL);

    socket.on('connect', () => {
      console.log('Socket connected:', socket.id);
    });

    socket.on('disconnect', () => {
      console.log('Socket disconnected');
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }

  return socket;
};

// Get the socket instance
export const getSocket = () => {
  if (!socket) {
    return initializeSocket();
  }
  return socket;
};

// Register as a delivery person
export const registerAsDeliveryPerson = (deliveryPersonId) => {
  const socket = getSocket();
  socket.emit('deliveryPersonLogin', deliveryPersonId);
};

// Unregister as a delivery person
export const unregisterAsDeliveryPerson = (deliveryPersonId) => {
  const socket = getSocket();
  socket.emit('deliveryPersonLogout', deliveryPersonId);
};

// Register as a user
export const registerAsUser = (userId) => {
  const socket = getSocket();
  socket.emit('userLogin', userId);
};

// Unregister as a user
export const unregisterAsUser = (userId) => {
  const socket = getSocket();
  socket.emit('userLogout', userId);
};

// Listen for new orders (for delivery persons)
export const listenForNewOrders = (callback) => {
  const socket = getSocket();

  socket.on('newOrder', (orderData) => {
    // Show a toast notification
    toast.info('New order available!', {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });

    // Call the callback with the order data
    if (callback) {
      callback(orderData);
    }
  });

  // Return a cleanup function
  return () => {
    socket.off('newOrder');
  };
};

// Listen for order taken events (for delivery persons)
export const listenForOrderTaken = (callback) => {
  const socket = getSocket();

  socket.on('orderTaken', (data) => {
    // Call the callback with the order data
    if (callback) {
      callback(data);
    }
  });

  // Return a cleanup function
  return () => {
    socket.off('orderTaken');
  };
};

// Listen for order accepted events (for users)
export const listenForOrderAccepted = (callback) => {
  const socket = getSocket();

  socket.on('orderAccepted', (data) => {
    // Show a toast notification
    toast.success('Your order has been accepted by a delivery partner!', {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });

    // Call the callback with the order data
    if (callback) {
      callback(data);
    }
  });

  // Return a cleanup function
  return () => {
    socket.off('orderAccepted');
  };
};

// Listen for order status updates (for users)
export const listenForOrderStatusUpdates = (orderId, callback) => {
  const socket = getSocket();

  socket.on(`orderStatus:${orderId}`, (data) => {
    // Show a toast notification based on status
    const statusMessages = {
      'PREPARING': 'Your order is being prepared!',
      'OUT_FOR_DELIVERY': 'Your order is out for delivery!',
      'DELIVERED': 'Your order has been delivered!'
    };

    if (statusMessages[data.status]) {
      toast.info(statusMessages[data.status], {
        position: 'top-right',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }

    // Call the callback with the status data
    if (callback) {
      callback(data);
    }
  });

  // Return a cleanup function
  return () => {
    socket.off(`orderStatus:${orderId}`);
  };
};

// Send location updates (for delivery persons)
export const sendLocationUpdate = (orderId, location) => {
  const socket = getSocket();
  socket.emit('locationUpdate', {
    orderId,
    location
  });
};

// Update order status (for delivery persons)
export const updateOrderStatus = (orderId, status, deliveryPersonId) => {
  const socket = getSocket();
  socket.emit('updateOrderStatus', {
    orderId,
    status,
    deliveryPersonId
  });
};

// Listen for location updates (for users)
export const listenForLocationUpdates = (orderId, callback) => {
  const socket = getSocket();

  socket.on(`locationUpdate:${orderId}`, (locationData) => {
    // Call the callback with the location data
    if (callback) {
      callback(locationData);
    }
  });

  // Return a cleanup function
  return () => {
    socket.off(`locationUpdate:${orderId}`);
  };
};

// Start sharing location (for delivery persons)
export const startLocationSharing = (deliveryPersonId, orderId) => {
  const socket = getSocket();
  let watchId = null;
  let intervalId = null;

  // Function to get and send location
  const getAndSendLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy,
            heading: position.coords.heading,
            speed: position.coords.speed,
            timestamp: new Date().toISOString()
          };

          // Send location update
          socket.emit('locationUpdate', {
            deliveryPersonId,
            orderId,
            location
          });

          console.log('Location update sent:', location);
        },
        (error) => {
          console.error('Error getting location:', error);
        },
        {
          enableHighAccuracy: true,
          maximumAge: 0,
          timeout: 10000
        }
      );
    }
  };

  // Get initial location immediately
  getAndSendLocation();

  // Set up interval to update location every 10 seconds
  intervalId = setInterval(getAndSendLocation, 10000);

  // Also use watchPosition for more accurate updates when moving
  watchId = navigator.geolocation.watchPosition(
    (position) => {
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
        accuracy: position.coords.accuracy,
        heading: position.coords.heading,
        speed: position.coords.speed,
        timestamp: new Date().toISOString()
      };

      // Send location update
      socket.emit('locationUpdate', {
        deliveryPersonId,
        orderId,
        location
      });

      console.log('Watch location update sent:', location);
    },
    (error) => {
      console.error('Error watching location:', error);
    },
    {
      enableHighAccuracy: true,
      maximumAge: 0,
      timeout: 10000
    }
  );

  // Return both IDs for cleanup
  return { watchId, intervalId };
};

// Stop sharing location (for delivery persons)
export const stopLocationSharing = (locationIds) => {
  if (locationIds) {
    if (locationIds.watchId) {
      navigator.geolocation.clearWatch(locationIds.watchId);
    }
    if (locationIds.intervalId) {
      clearInterval(locationIds.intervalId);
    }
  }
};

// Disconnect the socket
export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};
