// Development configuration display component
import React, { useState } from 'react';
import config from '../../config/environment.js';

const DevConfig = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development mode
  if (!config.IS_DEVELOPMENT) {
    return null;
  }

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 bg-purple-600 text-white p-2 rounded-full shadow-lg hover:bg-purple-700 transition-colors z-50"
        title="Toggle Dev Config"
      >
        ⚙️
      </button>

      {/* Config panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 bg-white border border-gray-300 rounded-lg shadow-xl p-4 max-w-md max-h-96 overflow-y-auto z-50">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-bold text-gray-800">Dev Configuration</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3 text-sm">
            {/* Environment Info */}
            <div>
              <h4 className="font-semibold text-gray-700 mb-1">Environment</h4>
              <div className="bg-gray-100 p-2 rounded">
                <div><strong>Mode:</strong> {config.MODE}</div>
                <div><strong>Node Env:</strong> {config.NODE_ENV}</div>
                <div><strong>Debug:</strong> {config.DEBUG ? '✅' : '❌'}</div>
                <div><strong>Logging:</strong> {config.ENABLE_LOGGING ? '✅' : '❌'}</div>
              </div>
            </div>

            {/* API Configuration */}
            <div>
              <h4 className="font-semibold text-gray-700 mb-1">API Configuration</h4>
              <div className="bg-gray-100 p-2 rounded">
                <div><strong>API Base:</strong> {config.API_BASE_URL}</div>
                <div><strong>Socket URL:</strong> {config.SOCKET_URL}</div>
              </div>
            </div>

            {/* App Info */}
            <div>
              <h4 className="font-semibold text-gray-700 mb-1">App Info</h4>
              <div className="bg-gray-100 p-2 rounded">
                <div><strong>Name:</strong> {config.APP_NAME}</div>
                <div><strong>Version:</strong> {config.APP_VERSION}</div>
              </div>
            </div>

            {/* Feature Flags */}
            <div>
              <h4 className="font-semibold text-gray-700 mb-1">Features</h4>
              <div className="bg-gray-100 p-2 rounded">
                <div><strong>Socket:</strong> {config.FEATURES.ENABLE_SOCKET ? '✅' : '❌'}</div>
                <div><strong>Notifications:</strong> {config.FEATURES.ENABLE_NOTIFICATIONS ? '✅' : '❌'}</div>
                <div><strong>Location:</strong> {config.FEATURES.ENABLE_LOCATION_TRACKING ? '✅' : '❌'}</div>
              </div>
            </div>

            {/* API Endpoints */}
            <div>
              <h4 className="font-semibold text-gray-700 mb-1">API Endpoints</h4>
              <div className="bg-gray-100 p-2 rounded max-h-32 overflow-y-auto">
                {Object.entries(config.API_ENDPOINTS).map(([key, value]) => (
                  <div key={key} className="text-xs">
                    <strong>{key}:</strong> {value}
                  </div>
                ))}
              </div>
            </div>

            {/* Authentication */}
            {config.CLERK_PUBLISHABLE_KEY && (
              <div>
                <h4 className="font-semibold text-gray-700 mb-1">Authentication</h4>
                <div className="bg-gray-100 p-2 rounded">
                  <div className="text-xs">
                    <strong>Clerk Key:</strong> {config.CLERK_PUBLISHABLE_KEY.substring(0, 20)}...
                  </div>
                </div>
              </div>
            )}

            {/* Test API Connection */}
            <div>
              <button
                onClick={async () => {
                  try {
                    const response = await fetch(`${config.API_BASE_URL}/user-api/test`);
                    if (response.ok) {
                      alert('✅ API Connection Successful!');
                    } else {
                      alert('❌ API Connection Failed!');
                    }
                  } catch (error) {
                    alert(`❌ API Connection Error: ${error.message}`);
                  }
                }}
                className="w-full bg-blue-500 text-white py-1 px-2 rounded text-xs hover:bg-blue-600"
              >
                Test API Connection
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DevConfig;
