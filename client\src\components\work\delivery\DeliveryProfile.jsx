import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useDeliveryAuth } from '../../../context/DeliveryAuthContext';

// API base URL
const API_BASE_URL = 'http://localhost:3000';

const DeliveryProfile = ({ profile }) => {
  const { currentDeliveryPartner, setCurrentDeliveryPartner } = useDeliveryAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: profile?.firstName || '',
    lastName: profile?.lastName || '',
    email: profile?.email || '',
    mobileNumber: profile?.mobileNumber || '',
    vehicleType: profile?.vehicle ? 'bike' : 'bicycle',
    vehicleNumber: profile?.vehicleNumber || '',
    bankAccount: profile?.bankAccount || '',
    ifscCode: profile?.ifscCode || '',
    address: profile?.address || ''
  });

  // Update form data when profile changes
  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        email: profile.email || '',
        mobileNumber: profile.mobileNumber || '',
        vehicleType: profile.vehicle ? 'bike' : 'bicycle',
        vehicleNumber: profile.vehicleNumber || '',
        bankAccount: profile.bankAccount || '',
        ifscCode: profile.ifscCode || '',
        address: profile.address || ''
      });
    }
  }, [profile]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Prepare the data to update
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        vechicle: formData.vehicleType === 'bike' || formData.vehicleType === 'scooter' || formData.vehicleType === 'car',
        // Include the new fields
        vehicleType: formData.vehicleType,
        vehicleNumber: formData.vehicleNumber,
        bankAccount: formData.bankAccount,
        ifscCode: formData.ifscCode,
        address: formData.address
      };

      // Call the API to update the profile
      const response = await axios.put(
        `${API_BASE_URL}/delivery-api/deliveryPersonupdate/${profile.id}`,
        updateData
      );

      if (response.data && response.data.payload) {
        // Update the current delivery partner in context
        setCurrentDeliveryPartner({
          ...currentDeliveryPartner,
          ...response.data.payload
        });

        // Update local storage
        localStorage.setItem('deliveryPartner', JSON.stringify({
          ...currentDeliveryPartner,
          ...response.data.payload
        }));

        toast.success('Profile updated successfully!');
        setIsEditing(false);
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Toggle availability status
  const toggleAvailability = () => {
    // In a real app, you would call an API to update the status
    toast.success(`You are now ${profile.status === 'ACTIVE' ? 'offline' : 'online'}`);
  };

  return (
    <div className="bg-white rounded-lg">
      {/* Profile Header */}
      <div className="flex flex-col md:flex-row items-center p-6 border-b">
        <div className="flex-shrink-0 mb-4 md:mb-0">
          <img
            src={profile.profileImg || 'https://ui-avatars.com/api/?name=' + profile.firstName}
            alt={`${profile.firstName} ${profile.lastName}`}
            className="h-24 w-24 rounded-full object-cover border-4 border-[#8a4af3]/20"
          />
        </div>
        <div className="md:ml-6 text-center md:text-left">
          <h2 className="text-xl font-bold">{profile.firstName} {profile.lastName}</h2>
          <p className="text-gray-600">Delivery Partner</p>
          <div className="flex items-center justify-center md:justify-start mt-2">
            <div className="flex items-center mr-4">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span className="ml-1 text-gray-700">{profile.rating.toFixed(1)}</span>
            </div>
            <div className="flex items-center">
              <span className={`inline-block h-2 w-2 rounded-full ${profile.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-500'}`}></span>
              <span className="ml-1 text-gray-700">{profile.status === 'ACTIVE' ? 'Online' : 'Offline'}</span>
            </div>
          </div>
        </div>
        <div className="mt-4 md:mt-0 md:ml-auto">
          <button
            onClick={toggleAvailability}
            className={`px-4 py-2 rounded-md text-white font-medium ${
              profile.status === 'ACTIVE'
                ? 'bg-red-500 hover:bg-red-600'
                : 'bg-[#8a4af3] hover:bg-[#7a3ad3]'
            } transition-colors`}
          >
            {profile.status === 'ACTIVE' ? 'Go Offline' : 'Go Online'}
          </button>
        </div>
      </div>

      {/* Profile Content */}
      <div className="p-6">
        {isEditing ? (
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
                <input
                  type="tel"
                  name="mobileNumber"
                  value={formData.mobileNumber}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                  readOnly
                />
                <p className="text-xs text-gray-500 mt-1">Mobile number cannot be changed</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Vehicle Type</label>
                <select
                  name="vehicleType"
                  value={formData.vehicleType}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                >
                  <option value="bike">Bike</option>
                  <option value="scooter">Scooter</option>
                  <option value="bicycle">Bicycle</option>
                  <option value="car">Car</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Vehicle Number</label>
                <input
                  type="text"
                  name="vehicleNumber"
                  value={formData.vehicleNumber}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Bank Account Number</label>
                <input
                  type="text"
                  name="bankAccount"
                  value={formData.bankAccount}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">IFSC Code</label>
                <input
                  type="text"
                  name="ifscCode"
                  value={formData.ifscCode}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[#8a4af3] focus:border-[#8a4af3]"
                  required
                ></textarea>
              </div>
            </div>
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#8a4af3] text-white rounded-md hover:bg-[#7a3ad3]"
              >
                Save Changes
              </button>
            </div>
          </form>
        ) : (
          <div>
            <div className="flex justify-end mb-4">
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center"
              >
                <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                Edit Profile
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Full Name</p>
                    <p className="font-medium">{profile.firstName} {profile.lastName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{profile.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Mobile Number</p>
                    <p className="font-medium">{profile.mobileNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Address</p>
                    <p className="font-medium">{formData.address || 'Not provided'}</p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Vehicle & Payment Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Vehicle Type</p>
                    <p className="font-medium capitalize">{formData.vehicleType || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Vehicle Number</p>
                    <p className="font-medium">{formData.vehicleNumber || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Bank Account</p>
                    <p className="font-medium">
                      {formData.bankAccount
                        ? `XXXX${formData.bankAccount.slice(-4)}`
                        : 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">IFSC Code</p>
                    <p className="font-medium">{formData.ifscCode || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Documents Section */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold mb-4">Documents</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">ID Proof</h4>
                      <p className="text-xs text-gray-500">Aadhar Card</p>
                    </div>
                    <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Verified</span>
                  </div>
                  <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
                    <svg className="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">Driving License</h4>
                      <p className="text-xs text-gray-500">Valid till Dec 2025</p>
                    </div>
                    <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Verified</span>
                  </div>
                  <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
                    <svg className="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">Vehicle RC</h4>
                      <p className="text-xs text-gray-500">Registration Certificate</p>
                    </div>
                    <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Verified</span>
                  </div>
                  <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
                    <svg className="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeliveryProfile;
