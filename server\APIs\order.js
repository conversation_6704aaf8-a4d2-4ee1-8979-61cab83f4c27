const express = require('express');
const orderApp = express.Router();
const mongoose = require('mongoose');
const orderModel = require('../models/orderModel');
const userModel = require('../models/userModel');
const shopItemsModel = require('../models/shopItemsModel');
const shopGoodsModel = require('../models/shopGoodsModel');
const expressAsyncHandler = require('express-async-handler');
const { notifyAllDeliveryPersons, notifyUser } = require('../services/socketService');

// Create a new order
orderApp.post('/order', expressAsyncHandler(async (req, res) => {
  try {
    const orderData = req.body;

    // Validate user exists
    const user = await userModel.findById(orderData.userId);
    if (!user) {
      return res.status(404).send({ message: "User not found" });
    }

    // Create new order
    const newOrder = new orderModel(orderData);

    // Ensure order status is set to CONFIRMED
    newOrder.orderStatus = 'CONFIRMED';

    // Process payment details
    if (orderData.paymentId) {
      // If payment ID is provided, add it to the order
      newOrder.paymentId = orderData.paymentId;

      // Add payment details if available
      if (orderData.paymentDetails) {
        newOrder.paymentDetails = orderData.paymentDetails;
      } else {
        // Set default payment details based on payment method
        newOrder.paymentDetails = {
          gateway: 'HomeXpert',
          transactionId: orderData.paymentId,
          paymentTimestamp: new Date(),
          paymentMethod: orderData.paymentMethod,
          paymentFee: 0,
          paymentNotes: orderData.paymentMethod === 'COD' ? 'Cash on Delivery' : 'Online Payment'
        };
      }
    }

    const savedOrder = await newOrder.save();

    // Notify all delivery persons about the new order
    notifyAllDeliveryPersons('newOrder', {
      orderId: savedOrder._id,
      customerName: savedOrder.deliveryAddress?.fullName || 'Customer',
      customerAddress: savedOrder.deliveryAddress ?
        `${savedOrder.deliveryAddress.addressLine1}, ${savedOrder.deliveryAddress.city}` :
        'Address not provided',
      orderAmount: savedOrder.totalAmount,
      orderItems: savedOrder.orderItems.map(item => ({
        name: Array.isArray(item.name) ? item.name[0] : item.name,
        quantity: item.quantity
      })),
      createdAt: savedOrder.createdAt
    });

    res.status(201).send({
      message: "Order placed successfully",
      payload: savedOrder
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to place order",
      error: error.message
    });
  }
}));

// Get all orders for a user
orderApp.get('/orders/:userId', expressAsyncHandler(async (req, res) => {
  try {
    const userId = req.params.userId;

    // Find all orders for the user
    const orders = await orderModel.find({ userId }).sort({ createdAt: -1 });

    res.status(200).send({
      message: "Orders fetched successfully",
      payload: orders
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to fetch orders",
      error: error.message
    });
  }
}));

// Get a specific order by ID
orderApp.get('/order/:orderId', expressAsyncHandler(async (req, res) => {
  try {
    const orderId = req.params.orderId;

    // Find the order
    const order = await orderModel.findById(orderId);

    if (!order) {
      return res.status(404).send({ message: "Order not found" });
    }

    res.status(200).send({
      message: "Order fetched successfully",
      payload: order
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to fetch order",
      error: error.message
    });
  }
}));

// Update order status
orderApp.patch('/order/:orderId/status', expressAsyncHandler(async (req, res) => {
  try {
    const orderId = req.params.orderId;
    const { orderStatus } = req.body;

    // First, find the order to check its payment method
    const order = await orderModel.findById(orderId);

    if (!order) {
      return res.status(404).send({ message: "Order not found" });
    }

    // Prepare update object based on order status and payment method
    const updateObj = {
      orderStatus
    };

    // Add additional fields based on status
    if (orderStatus === 'DELIVERED') {
      updateObj.deliveredAt = new Date();

      // If payment method is COD, update payment status to PAID when delivered
      if (order.paymentMethod === 'COD') {
        updateObj.paymentStatus = 'PAID';
      }
    } else if (orderStatus === 'CANCELLED') {
      updateObj.cancelledAt = new Date();
      updateObj.cancelReason = req.body.cancelReason;
      updateObj.paymentStatus = 'CANCELLED';
    }

    // Update the order with the prepared object
    const updatedOrder = await orderModel.findByIdAndUpdate(
      orderId,
      updateObj,
      { new: true }
    );

    res.status(200).send({
      message: "Order status updated successfully",
      payload: updatedOrder
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).send({
      message: "Failed to update order status",
      error: error.message
    });
  }
}));

// Update payment status
orderApp.patch('/order/:orderId/payment', expressAsyncHandler(async (req, res) => {
  try {
    const orderId = req.params.orderId;
    const { paymentStatus, paymentId, paymentDetails } = req.body;

    // Validate payment status
    const validPaymentStatuses = ['PENDING', 'PAID', 'FAILED', 'REFUNDED', 'CANCELLED'];
    if (!validPaymentStatuses.includes(paymentStatus)) {
      return res.status(400).send({
        message: "Invalid payment status",
        validStatuses: validPaymentStatuses
      });
    }

    // Find the order
    const order = await orderModel.findById(orderId);
    if (!order) {
      return res.status(404).send({ message: "Order not found" });
    }

    // Update payment fields
    const updateData = { paymentStatus };

    if (paymentId) {
      updateData.paymentId = paymentId;
    }

    if (paymentDetails) {
      // If payment details are provided, merge with existing details
      updateData.paymentDetails = {
        ...order.paymentDetails,
        ...paymentDetails,
        paymentTimestamp: new Date() // Always update timestamp
      };
    }

    // Update the order
    const updatedOrder = await orderModel.findByIdAndUpdate(
      orderId,
      updateData,
      { new: true }
    );

    res.status(200).send({
      message: "Payment status updated successfully",
      payload: updatedOrder
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to update payment status",
      error: error.message
    });
  }
}));

// Add rating and feedback to an order
orderApp.patch('/order/:orderId/feedback', expressAsyncHandler(async (req, res) => {
  try {
    const orderId = req.params.orderId;
    const { rating, feedback } = req.body;

    // Find and update the order
    const updatedOrder = await orderModel.findByIdAndUpdate(
      orderId,
      { rating, feedback },
      { new: true }
    );

    if (!updatedOrder) {
      return res.status(404).send({ message: "Order not found" });
    }

    res.status(200).send({
      message: "Feedback added successfully",
      payload: updatedOrder
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to add feedback",
      error: error.message
    });
  }
}));

// Get recent orders for a user (last 5 orders)
orderApp.get('/orders/:userId/recent', expressAsyncHandler(async (req, res) => {
  try {
    const userId = req.params.userId;

    // Find recent orders for the user
    const recentOrders = await orderModel.find({ userId })
      .sort({ createdAt: -1 })
      .limit(5);

    res.status(200).send({
      message: "Recent orders fetched successfully",
      payload: recentOrders
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to fetch recent orders",
      error: error.message
    });
  }
}));

// Get orders for a delivery person
orderApp.get('/orders/delivery/:deliveryPersonId', expressAsyncHandler(async (req, res) => {
  try {
    const deliveryPersonId = req.params.deliveryPersonId;

    // Validate the deliveryPersonId
    if (!mongoose.Types.ObjectId.isValid(deliveryPersonId)) {
      return res.status(400).send({
        message: "Invalid delivery person ID format",
        payload: []
      });
    }

    // Find orders assigned to this delivery person
    // Include all relevant statuses including CONFIRMED, PREPARING, OUT_FOR_DELIVERY
    const assignedOrders = await orderModel.find({
      deliveryPersonId,
      orderStatus: { $in: ['CONFIRMED', 'PREPARING', 'OUT_FOR_DELIVERY'] }
    }).sort({ createdAt: -1 });

    // Return empty array if no orders found
    res.status(200).send({
      message: assignedOrders.length > 0 ? "Delivery orders fetched successfully" : "No orders found for this delivery person",
      payload: assignedOrders
    });
  } catch (error) {
    console.error('Error fetching delivery orders:', error);
    // Return empty array instead of error to prevent UI issues
    res.status(200).send({
      message: "Failed to fetch delivery orders, but returning empty array",
      payload: []
    });
  }
}));

// Get available orders for delivery (not yet assigned to any delivery person)
orderApp.get('/orders/available', expressAsyncHandler(async (req, res) => {
  try {
    // Find orders that are confirmed but not assigned to any delivery person
    const availableOrders = await orderModel.find({
      deliveryPersonId: { $exists: false },
      orderStatus: 'CONFIRMED'
    }).sort({ createdAt: -1 });

    res.status(200).send({
      message: availableOrders.length > 0 ? "Available orders fetched successfully" : "No available orders found",
      payload: availableOrders
    });
  } catch (error) {
    console.error('Error fetching available orders:', error);
    // Return empty array instead of error to prevent UI issues
    res.status(200).send({
      message: "Failed to fetch available orders, but returning empty array",
      payload: []
    });
  }
}));

// Accept an order (assign to delivery person)
orderApp.post('/order/:orderId/accept', expressAsyncHandler(async (req, res) => {
  try {
    const orderId = req.params.orderId;
    const { deliveryPersonId } = req.body;

    if (!deliveryPersonId) {
      return res.status(400).send({ message: "Delivery person ID is required" });
    }

    // Find the order
    const order = await orderModel.findById(orderId);

    if (!order) {
      return res.status(404).send({ message: "Order not found" });
    }

    if (order.deliveryPersonId) {
      return res.status(400).send({ message: "Order already assigned to a delivery person" });
    }

    // Get delivery person details
    const deliveryPersonModel = require('../models/deliveryPersonModel');
    const deliveryPerson = await deliveryPersonModel.findById(deliveryPersonId);

    if (!deliveryPerson) {
      return res.status(404).send({ message: "Delivery person not found" });
    }

    // Update the order with delivery person ID and change status to OUT_FOR_DELIVERY
    const updatedOrder = await orderModel.findByIdAndUpdate(
      orderId,
      {
        deliveryPersonId,
        orderStatus: 'OUT_FOR_DELIVERY',
        expectedDeliveryTime: new Date(Date.now() + 30 * 60000), // 30 minutes from now
        deliveryPersonDetails: {
          name: `${deliveryPerson.firstName} ${deliveryPerson.lastName || ''}`.trim(),
          mobileNumber: deliveryPerson.mobileNumber,
          profileImg: deliveryPerson.profileImg || '',
          vehicleType: deliveryPerson.vehicleType || 'bike',
          vehicleNumber: deliveryPerson.vehicleNumber || ''
        }
      },
      { new: true }
    );

    // Notify the user that their order has been accepted
    notifyUser(order.userId, 'orderAccepted', {
      orderId: updatedOrder._id,
      deliveryPerson: {
        name: `${deliveryPerson.firstName} ${deliveryPerson.lastName || ''}`.trim(),
        mobileNumber: deliveryPerson.mobileNumber,
        profileImg: deliveryPerson.profileImg || '',
        vehicleType: deliveryPerson.vehicleType || 'bike',
        vehicleNumber: deliveryPerson.vehicleNumber || ''
      },
      expectedDeliveryTime: updatedOrder.expectedDeliveryTime
    });

    // Notify all other delivery persons that this order is no longer available
    notifyAllDeliveryPersons('orderTaken', {
      orderId: updatedOrder._id
    });

    res.status(200).send({
      message: "Order accepted successfully",
      payload: updatedOrder
    });
  } catch (error) {
    res.status(500).send({
      message: "Failed to accept order",
      error: error.message
    });
  }
}));

// Get completed orders for a delivery person
orderApp.get('/orders/delivery/:deliveryPersonId/completed', expressAsyncHandler(async (req, res) => {
  try {
    const deliveryPersonId = req.params.deliveryPersonId;

    // Validate the deliveryPersonId
    if (!mongoose.Types.ObjectId.isValid(deliveryPersonId)) {
      return res.status(400).send({
        message: "Invalid delivery person ID format",
        payload: []
      });
    }

    // Find completed orders for this delivery person
    const completedOrders = await orderModel.find({
      deliveryPersonId,
      orderStatus: 'DELIVERED'
    }).sort({ deliveredAt: -1, createdAt: -1 });

    // Return empty array if no orders found
    res.status(200).send({
      message: completedOrders.length > 0 ? "Completed orders fetched successfully" : "No completed orders found for this delivery person",
      payload: completedOrders
    });
  } catch (error) {
    console.error('Error fetching completed orders:', error);
    // Return empty array instead of error to prevent UI issues
    res.status(200).send({
      message: "Failed to fetch completed orders, but returning empty array",
      payload: []
    });
  }
}));

// Get delivery person statistics
orderApp.get('/orders/delivery/:deliveryPersonId/stats', expressAsyncHandler(async (req, res) => {
  try {
    const deliveryPersonId = req.params.deliveryPersonId;

    // Validate the deliveryPersonId
    if (!mongoose.Types.ObjectId.isValid(deliveryPersonId)) {
      return res.status(200).send({
        message: "Invalid delivery person ID format",
        payload: {
          totalDeliveries: 0,
          pendingDeliveries: 0,
          totalEarnings: 0
        }
      });
    }

    // Find all orders for this delivery person
    const allOrders = await orderModel.find({ deliveryPersonId });

    // Calculate statistics
    const totalDeliveries = allOrders.filter(order =>
      order.orderStatus === 'DELIVERED'
    ).length;

    const pendingDeliveries = allOrders.filter(order =>
      ['CONFIRMED', 'PREPARING', 'OUT_FOR_DELIVERY'].includes(order.orderStatus)
    ).length;

    // Calculate total earnings (only from delivered orders)
    const totalEarnings = allOrders
      .filter(order => order.orderStatus === 'DELIVERED')
      .reduce((sum, order) => sum + (order.deliveryFee || 0), 0);

    res.status(200).send({
      message: "Delivery statistics fetched successfully",
      payload: {
        totalDeliveries,
        pendingDeliveries,
        totalEarnings
      }
    });
  } catch (error) {
    console.error('Error fetching delivery statistics:', error);
    // Return default values instead of error to prevent UI issues
    res.status(200).send({
      message: "Failed to fetch delivery statistics, but returning default values",
      payload: {
        totalDeliveries: 0,
        pendingDeliveries: 0,
        totalEarnings: 0
      }
    });
  }
}));

module.exports = orderApp;
