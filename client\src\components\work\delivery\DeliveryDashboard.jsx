import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useDeliveryAuth } from '../../../context/DeliveryAuthContext';
import DeliveryStats from './DeliveryStats';
import OrdersList from './OrdersList';
import OrderHistory from './OrderHistory';
import DeliveryProfile from './DeliveryProfile';
import {
  initializeSocket,
  registerAsDeliveryPerson,
  listenForNewOrders,
  listenForOrderTaken
} from '../../../services/socketService';

// API base URL
const API_BASE_URL = 'http://localhost:3000';

const DeliveryDashboard = () => {
  const { currentDeliveryPartner, logoutDeliveryPartner } = useDeliveryAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('orders');
  const [deliveryProfile, setDeliveryProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [completedOrders, setCompletedOrders] = useState([]);
  const [newOrderNotification, setNewOrderNotification] = useState(null);
  const [stats, setStats] = useState({
    totalEarnings: 0,
    totalDeliveries: 0,
    pendingDeliveries: 0,
    rating: 0
  });

  // Initialize socket connection
  useEffect(() => {
    if (currentDeliveryPartner && currentDeliveryPartner._id) {
      // Initialize socket
      initializeSocket();

      // Register as a delivery person
      registerAsDeliveryPerson(currentDeliveryPartner._id);

      // Listen for new orders
      const cleanupNewOrders = listenForNewOrders((orderData) => {
        // Add the new order to the orders list
        setOrders(prevOrders => {
          // Check if the order is already in the list
          const orderExists = prevOrders.some(order => order.id === orderData.orderId);

          if (orderExists) {
            return prevOrders;
          }

          // Create a new order object
          const newOrder = {
            id: orderData.orderId,
            customerName: orderData.customerName,
            customerAddress: orderData.customerAddress,
            items: orderData.orderItems,
            status: 'PENDING',
            createdAt: orderData.createdAt,
            estimatedDeliveryTime: '30 mins',
            amount: orderData.orderAmount,
            paymentMethod: 'Not specified'
          };

          // Set notification
          setNewOrderNotification(newOrder);

          // Add to orders list
          return [newOrder, ...prevOrders];
        });
      });

      // Listen for order taken events
      const cleanupOrderTaken = listenForOrderTaken((data) => {
        // Remove the order from the list if it was taken by another delivery person
        setOrders(prevOrders => prevOrders.filter(order => order.id !== data.orderId));

        // Clear notification if this was the notified order
        if (newOrderNotification && newOrderNotification.id === data.orderId) {
          setNewOrderNotification(null);
        }
      });

      // Cleanup function
      return () => {
        cleanupNewOrders();
        cleanupOrderTaken();
      };
    }
  }, [currentDeliveryPartner]);

  // Fetch delivery person profile
  useEffect(() => {
    if (!currentDeliveryPartner) {
      navigate('/work/delivery/login');
      return;
    }

    const fetchDeliveryProfile = async () => {
      try {
        setLoading(true);

        console.log('Current delivery partner:', currentDeliveryPartner);

        // First try to use the currentDeliveryPartner from context
        if (currentDeliveryPartner) {
          const profile = currentDeliveryPartner;

          // Fetch delivery statistics
          try {
            const statsResponse = await axios.get(
              `${API_BASE_URL}/order-api/orders/delivery/${profile._id}/stats`
            );

            const deliveryStats = statsResponse.data?.payload || {
              totalEarnings: 0,
              totalDeliveries: 0,
              pendingDeliveries: 0
            };

            setDeliveryProfile({
              id: profile._id,
              firstName: profile.firstName,
              lastName: profile.lastName || '',
              mobileNumber: profile.mobileNumber,
              email: profile.email || 'Not provided',
              profileImg: profile.profileImg || 'https://ui-avatars.com/api/?name=' + profile.firstName,
              vehicle: profile.vechicle || false,
              vehicleType: profile.vehicleType || 'bike',
              vehicleNumber: profile.vehicleNumber || '',
              bankAccount: profile.bankAccount || '',
              ifscCode: profile.ifscCode || '',
              address: profile.address || '',
              rating: profile.rating || 0,
              status: profile.status || 'ACTIVE',
              totalEarnings: deliveryStats.totalEarnings || 0,
              totalDeliveries: deliveryStats.totalDeliveries || 0,
              pendingDeliveries: deliveryStats.pendingDeliveries || 0
            });

            setStats({
              totalEarnings: deliveryStats.totalEarnings || 0,
              totalDeliveries: deliveryStats.totalDeliveries || 0,
              pendingDeliveries: deliveryStats.pendingDeliveries || 0,
              rating: profile.rating || 0
            });
          } catch (error) {
            console.error('Error fetching delivery stats:', error);

            // Set default values if stats fetch fails
            setDeliveryProfile({
              id: profile._id,
              firstName: profile.firstName,
              lastName: profile.lastName || '',
              mobileNumber: profile.mobileNumber,
              email: profile.email || 'Not provided',
              profileImg: profile.profileImg || 'https://ui-avatars.com/api/?name=' + profile.firstName,
              vehicle: profile.vechicle || false,
              vehicleType: profile.vehicleType || 'bike',
              vehicleNumber: profile.vehicleNumber || '',
              bankAccount: profile.bankAccount || '',
              ifscCode: profile.ifscCode || '',
              address: profile.address || '',
              rating: profile.rating || 0,
              status: profile.status || 'ACTIVE',
              totalEarnings: 0,
              totalDeliveries: 0,
              pendingDeliveries: 0
            });

            setStats({
              totalEarnings: 0,
              totalDeliveries: 0,
              pendingDeliveries: 0,
              rating: profile.rating || 0
            });
          }

          setLoading(false);
          return;
        }

        // If we don't have the profile in context, fetch it from the API
        try {
          const response = await axios.get(`${API_BASE_URL}/delivery-api/deliveryperson/${currentDeliveryPartner._id}`);

          if (response.data && response.data.payload) {
            const profile = response.data.payload;

            setDeliveryProfile({
              id: profile._id,
              firstName: profile.firstName,
              lastName: profile.lastName || '',
              mobileNumber: profile.mobileNumber,
              email: profile.email || 'Not provided',
              profileImg: profile.profileImg || 'https://ui-avatars.com/api/?name=' + profile.firstName,
              vehicle: profile.vechicle || false,
              vehicleType: profile.vehicleType || 'bike',
              vehicleNumber: profile.vehicleNumber || '',
              bankAccount: profile.bankAccount || '',
              ifscCode: profile.ifscCode || '',
              address: profile.address || '',
              rating: profile.rating || 0,
              status: profile.status || 'ACTIVE',
              totalEarnings: 0,
              totalDeliveries: 0,
              pendingDeliveries: 0
            });

            setStats({
              totalEarnings: 0,
              totalDeliveries: 0,
              pendingDeliveries: 0,
              rating: profile.rating || 0
            });
          } else {
            throw new Error('Failed to fetch delivery profile');
          }
        } catch (error) {
          console.error('Error fetching delivery profile from API:', error);
          toast.error('Failed to load delivery profile');
        }

        setLoading(false);
      } catch (error) {
        console.error('Error in fetchDeliveryProfile:', error);
        toast.error('Failed to load delivery profile');
        setLoading(false);
      }
    };

    fetchDeliveryProfile();
  }, [currentDeliveryPartner, navigate]);

  // Fetch orders
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);

        if (!currentDeliveryPartner || !currentDeliveryPartner._id) {
          console.error('No delivery partner ID found');
          setOrders([]);
          setLoading(false);
          return;
        }

        let assignedResponse, availableResponse;

        try {
          // Fetch assigned orders for this delivery person
          assignedResponse = await axios.get(
            `${API_BASE_URL}/order-api/orders/delivery/${currentDeliveryPartner._id}`
          );
        } catch (error) {
          console.error('Error fetching assigned orders:', error);
          assignedResponse = { data: { payload: [] } };
        }

        try {
          // Fetch available orders that can be accepted
          availableResponse = await axios.get(
            `${API_BASE_URL}/order-api/orders/available`
          );
        } catch (error) {
          console.error('Error fetching available orders:', error);
          availableResponse = { data: { payload: [] } };
        }

        let formattedOrders = [];

        // Format assigned orders
        if (assignedResponse.data && assignedResponse.data.payload) {
          const assignedOrders = assignedResponse.data.payload.map(order => {
            try {
              return {
                id: order._id,
                customerName: order.deliveryAddress?.fullName || 'Customer',
                customerAddress: order.deliveryAddress ?
                  `${order.deliveryAddress.addressLine1 || ''}, ${order.deliveryAddress.city || ''}, ${order.deliveryAddress.pincode || ''}` :
                  'Address not available',
                items: order.orderItems ? order.orderItems.map(item => ({
                  name: Array.isArray(item.name) ? item.name[0] : (item.name || 'Item'),
                  quantity: item.quantity || 1
                })) : [],
                status: 'ASSIGNED',
                createdAt: order.createdAt,
                estimatedDeliveryTime: order.expectedDeliveryTime
                  ? new Date(order.expectedDeliveryTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                  : '30 mins',
                amount: order.totalAmount || 0,
                paymentMethod: order.paymentMethod || 'Cash'
              };
            } catch (err) {
              console.error('Error formatting assigned order:', err);
              return null;
            }
          }).filter(order => order !== null);

          formattedOrders = [...formattedOrders, ...assignedOrders];
        }

        // Format available orders
        if (availableResponse.data && availableResponse.data.payload) {
          const availableOrders = availableResponse.data.payload.map(order => {
            try {
              return {
                id: order._id,
                customerName: order.deliveryAddress?.fullName || 'Customer',
                customerAddress: order.deliveryAddress ?
                  `${order.deliveryAddress.addressLine1 || ''}, ${order.deliveryAddress.city || ''}, ${order.deliveryAddress.pincode || ''}` :
                  'Address not available',
                items: order.orderItems ? order.orderItems.map(item => ({
                  name: Array.isArray(item.name) ? item.name[0] : (item.name || 'Item'),
                  quantity: item.quantity || 1
                })) : [],
                status: 'PENDING',
                createdAt: order.createdAt,
                estimatedDeliveryTime: '30 mins',
                amount: order.totalAmount || 0,
                paymentMethod: order.paymentMethod || 'Cash'
              };
            } catch (err) {
              console.error('Error formatting available order:', err);
              return null;
            }
          }).filter(order => order !== null);

          formattedOrders = [...formattedOrders, ...availableOrders];
        }

        setOrders(formattedOrders);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching orders:', error);
        // Don't show error toast, just set empty orders
        setOrders([]);
        setLoading(false);
      }
    };

    if (currentDeliveryPartner) {
      fetchOrders();
      fetchCompletedOrders();
    } else {
      setOrders([]);
      setCompletedOrders([]);
    }
  }, [currentDeliveryPartner]);

  // Fetch completed orders
  const fetchCompletedOrders = async () => {
    try {
      if (!currentDeliveryPartner || !currentDeliveryPartner._id) {
        console.error('No delivery partner ID found');
        setCompletedOrders([]);
        return;
      }

      // Fetch completed orders for this delivery person
      const response = await axios.get(
        `${API_BASE_URL}/order-api/orders/delivery/${currentDeliveryPartner._id}/completed`
      );

      let formattedOrders = [];

      // Format completed orders
      if (response.data && response.data.payload) {
        formattedOrders = response.data.payload.map(order => {
          try {
            return {
              id: order._id,
              customerName: order.deliveryAddress?.fullName || 'Customer',
              customerAddress: order.deliveryAddress ?
                `${order.deliveryAddress.addressLine1 || ''}, ${order.deliveryAddress.city || ''}, ${order.deliveryAddress.pincode || ''}` :
                'Address not available',
              items: order.orderItems ? order.orderItems.map(item => ({
                name: Array.isArray(item.name) ? item.name[0] : (item.name || 'Item'),
                quantity: item.quantity || 1
              })) : [],
              status: 'DELIVERED',
              createdAt: order.createdAt,
              deliveredAt: order.deliveredAt,
              amount: order.totalAmount || 0,
              deliveryFee: order.deliveryFee || 0,
              paymentMethod: order.paymentMethod || 'Cash'
            };
          } catch (err) {
            console.error('Error formatting completed order:', err);
            return null;
          }
        }).filter(order => order !== null);
      }

      setCompletedOrders(formattedOrders);
    } catch (error) {
      console.error('Error fetching completed orders:', error);
      setCompletedOrders([]);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 mt-16 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8a4af3]"></div>
      </div>
    );
  }

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'orders':
        return <OrdersList
          orders={orders}
          newOrderNotification={newOrderNotification}
          onDismissNotification={() => setNewOrderNotification(null)}
        />;
      case 'history':
        return <OrderHistory completedOrders={completedOrders} />;
      case 'earnings':
        return <DeliveryStats stats={stats} />;
      case 'profile':
        return <DeliveryProfile profile={deliveryProfile} />;
      default:
        return <OrdersList orders={orders} />;
    }
  };

  // Handle logout
  const handleLogout = () => {
    logoutDeliveryPartner();
    navigate('/work/delivery/login');
  };

  return (
    <div className="min-h-screen bg-gray-50 font-['Gilroy',arial,'Helvetica Neue',sans-serif] mt-16">
      <div className="container mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Header with profile summary */}
          <div className="bg-[#8a4af3] text-white p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <img
                    src={deliveryProfile.profileImg}
                    alt={`${deliveryProfile.firstName} ${deliveryProfile.lastName}`}
                    className="h-16 w-16 rounded-full object-cover border-2 border-white"
                  />
                </div>
                <div className="ml-4">
                  <h1 className="text-2xl font-bold">Welcome, {deliveryProfile.firstName}!</h1>
                  <div className="flex items-center mt-1">
                    <div className="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {deliveryProfile.status}
                    </div>
                    <div className="ml-3 flex items-center">
                      <svg className="h-5 w-5 text-yellow-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="ml-1 text-white">{deliveryProfile.rating.toFixed(1)}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-md transition-colors flex items-center"
                >
                  <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Logout
                </button>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
            <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-[#8a4af3]">
              <div className="flex items-center">
                <div className="p-3 bg-[#8a4af3]/10 rounded-full">
                  <svg className="h-6 w-6 text-[#8a4af3]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-gray-500 text-sm">Total Earnings</p>
                  <h3 className="text-2xl font-bold text-gray-800">₹{stats.totalEarnings.toLocaleString()}</h3>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-full">
                  <svg className="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-gray-500 text-sm">Total Deliveries</p>
                  <h3 className="text-2xl font-bold text-gray-800">{stats.totalDeliveries}</h3>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-amber-500">
              <div className="flex items-center">
                <div className="p-3 bg-amber-100 rounded-full">
                  <svg className="h-6 w-6 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-gray-500 text-sm">Pending Deliveries</p>
                  <h3 className="text-2xl font-bold text-gray-800">{stats.pendingDeliveries}</h3>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="px-6 border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              <button
                onClick={() => setActiveTab('orders')}
                className={`py-4 px-1 border-b-2 font-medium ${
                  activeTab === 'orders'
                    ? 'border-[#8a4af3] text-[#8a4af3]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  Orders
                </div>
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-4 px-1 border-b-2 font-medium ${
                  activeTab === 'history'
                    ? 'border-[#8a4af3] text-[#8a4af3]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  History
                </div>
              </button>
              <button
                onClick={() => setActiveTab('earnings')}
                className={`py-4 px-1 border-b-2 font-medium ${
                  activeTab === 'earnings'
                    ? 'border-[#8a4af3] text-[#8a4af3]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  Earnings
                </div>
              </button>
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-1 border-b-2 font-medium ${
                  activeTab === 'profile'
                    ? 'border-[#8a4af3] text-[#8a4af3]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Profile
                </div>
              </button>
            </nav>
          </div>

          {/* Content Area */}
          <div className="p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryDashboard;
