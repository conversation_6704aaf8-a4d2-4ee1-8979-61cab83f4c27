import React, { createContext, useState, useContext, useEffect } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';

// Create context
const DeliveryAuthContext = createContext();

// API base URL
const API_BASE_URL = 'http://localhost:3000';

// Context provider component
export const DeliveryAuthProvider = ({ children }) => {
  const [currentDeliveryPartner, setCurrentDeliveryPartner] = useState(null);
  const [loading, setLoading] = useState(true);
  const [otpSent, setOtpSent] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');

  // Load delivery partner from localStorage on initial render
  useEffect(() => {
    const storedPartner = localStorage.getItem('deliveryPartner');

    if (storedPartner) {
      try {
        const parsedPartner = JSON.parse(storedPartner);
        setCurrentDeliveryPartner(parsedPartner);

        // Verify the stored partner with the server
        verifyStoredPartner(parsedPartner._id);
      } catch (error) {
        console.error('Error parsing stored delivery partner:', error);
        localStorage.removeItem('deliveryPartner');
      }
    }

    setLoading(false);
  }, []);

  // Verify the stored partner with the server
  const verifyStoredPartner = async (partnerId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/delivery-api/deliveryperson/${partnerId}`);

      if (response.data && response.data.payload) {
        // Update the stored partner with the latest data from the server
        setCurrentDeliveryPartner(response.data.payload);
        localStorage.setItem('deliveryPartner', JSON.stringify(response.data.payload));
      } else {
        // If the partner doesn't exist on the server, clear the local storage
        setCurrentDeliveryPartner(null);
        localStorage.removeItem('deliveryPartner');
      }
    } catch (error) {
      console.error('Error verifying stored partner:', error);
      // If there's an error, we'll keep the stored partner for now
    }
  };

  // Update localStorage when currentDeliveryPartner changes
  useEffect(() => {
    if (currentDeliveryPartner) {
      localStorage.setItem('deliveryPartner', JSON.stringify(currentDeliveryPartner));
    } else {
      localStorage.removeItem('deliveryPartner');
    }
  }, [currentDeliveryPartner]);

  // Register a new delivery partner
  const registerDeliveryPartner = async (partnerData) => {
    try {
      // Generate a unique profile image URL with a timestamp to avoid uniqueness conflicts
      const timestamp = new Date().getTime();
      const uniqueProfileImg = `https://ui-avatars.com/api/?name=${partnerData.firstName}+${partnerData.lastName}&timestamp=${timestamp}`;

      // Format the data to match the backend model
      const formattedData = {
        // Add a unique deliveryPersonId to avoid duplicate key errors
        deliveryPersonId: `DP${timestamp}${Math.floor(Math.random() * 1000)}`,
        profileImg: uniqueProfileImg,
        // Format the mobile number with the country code for validation
        mobileNumber: `+91${partnerData.mobileNumber}`,
        firstName: partnerData.firstName,
        lastName: partnerData.lastName,
        email: partnerData.email || `${partnerData.firstName.toLowerCase()}${timestamp}@example.com`,
        dob: partnerData.dob,
        // Note: The backend model has a typo in 'vechicle' instead of 'vehicle'
        vechicle: partnerData.vehicleType !== 'bicycle', // true for bike/scooter, false for bicycle
      };

      console.log('Sending registration data:', formattedData);

      // Make an API call to register the delivery partner
      const response = await axios.post(`${API_BASE_URL}/delivery-api/deliveryPerson`, formattedData);

      if (response.data && response.data.payload) {
        toast.success('Registration successful! Your application is under review.');
        return { success: true, message: 'Registration successful! Your application is under review.' };
      } else {
        throw new Error('Failed to register. Please try again.');
      }
    } catch (error) {
      console.error('Registration error:', error);

      // Handle specific error cases
      if (error.response) {
        console.log('Error response:', error.response);

        // Handle duplicate key errors
        if (error.response.data && error.response.data.message && error.response.data.message.includes('duplicate key')) {
          if (error.response.data.message.includes('mobileNumber')) {
            throw new Error('A delivery partner with this mobile number already exists.');
          } else if (error.response.data.message.includes('email')) {
            throw new Error('A delivery partner with this email already exists.');
          } else if (error.response.data.message.includes('profileImg')) {
            // This should not happen with our unique timestamp approach, but just in case
            throw new Error('Please try again with a different profile image.');
          } else if (error.response.data.message.includes('deliveryPersonId')) {
            // This should not happen with our unique ID generation, but just in case
            throw new Error('Please try again. There was an issue with your registration.');
          }
        } else if (error.response.data && error.response.data.message) {
          throw new Error(error.response.data.message);
        }
      }

      // Generic error message as fallback
      throw new Error('Failed to register. Please try again later.');
    }
  };

  // Check if delivery partner exists by mobile number
  const checkDeliveryPartnerExists = async (phone) => {
    try {
      // Make an API call to check if the delivery partner exists
      const response = await axios.get(`${API_BASE_URL}/delivery-api/deliveryPerson/check/${phone}`);

      if (response.data) {
        return response.data.exists;
      }

      return false;
    } catch (error) {
      console.error('Check delivery partner error:', error);
      return false;
    }
  };

  // Send OTP to phone number
  const sendDeliveryOTP = async (phone) => {
    try {
      // For demo purposes, we'll still generate the OTP locally
      // In a production app, this would be handled by the server
      const otp = Math.floor(1000 + Math.random() * 9000).toString();

      // Store OTP in localStorage for verification
      localStorage.setItem('deliveryPartnerOtp', otp);
      localStorage.setItem('deliveryPartnerPhone', phone);

      setPhoneNumber(phone);
      setOtpSent(true);

      // In a real app, this would send the OTP via SMS
      toast.info(`OTP: ${otp} (For demo purposes only)`);

      return { success: true, message: 'OTP sent successfully', otp };
    } catch (error) {
      console.error('Send OTP error:', error);
      throw error;
    }
  };

  // Resend OTP to phone number
  const resendDeliveryOTP = async (phone) => {
    try {
      // In a real app, this would be an API call to resend OTP
      // For now, we'll simulate it

      // Generate a new random 4-digit OTP
      const otp = Math.floor(1000 + Math.random() * 9000).toString();

      // Store OTP in localStorage for verification
      localStorage.setItem('deliveryPartnerOtp', otp);

      return { success: true, message: 'OTP resent successfully', otp };
    } catch (error) {
      console.error('Resend OTP error:', error);
      throw error;
    }
  };

  // Verify OTP
  const verifyDeliveryOTP = async (otp) => {
    try {
      const storedOtp = localStorage.getItem('deliveryPartnerOtp');
      const storedPhone = localStorage.getItem('deliveryPartnerPhone');

      if (otp !== storedOtp) {
        throw new Error('Invalid OTP');
      }

      // Get delivery partner data from the server
      const response = await axios.get(`${API_BASE_URL}/delivery-api/deliveryPerson`);

      if (!response.data || !response.data.payload) {
        throw new Error('Failed to fetch delivery partner data');
      }

      const partners = response.data.payload;
      // Format the stored phone number to match the format in the database
      const formattedPhone = storedPhone.startsWith('+91') ? storedPhone : `+91${storedPhone}`;
      const partner = partners.find(p => p.mobileNumber === formattedPhone);

      if (!partner) {
        throw new Error('Delivery partner not found with this mobile number. Please register first.');
      }

      // In a real app, you would have status fields in your database
      // For now, we'll assume all partners in the database are active

      // Add status field if not present
      const partnerWithStatus = {
        ...partner,
        status: partner.status || 'ACTIVE'
      };

      // Set the current delivery partner in the context
      setCurrentDeliveryPartner(partnerWithStatus);
      setOtpSent(false);

      // Store in localStorage for persistence
      localStorage.setItem('deliveryPartner', JSON.stringify(partnerWithStatus));

      console.log('Delivery partner logged in:', partnerWithStatus);

      return { success: true, payload: partnerWithStatus };
    } catch (error) {
      console.error('Verify OTP error:', error);
      throw error;
    }
  };

  // Logout delivery partner
  const logoutDeliveryPartner = () => {
    setCurrentDeliveryPartner(null);
    localStorage.removeItem('deliveryPartner');
    localStorage.removeItem('deliveryPartnerOtp');
    localStorage.removeItem('deliveryPartnerPhone');
    toast.success('Logged out successfully');
  };

  // Context value
  const value = {
    currentDeliveryPartner,
    setCurrentDeliveryPartner,
    loading,
    otpSent,
    setOtpSent,
    phoneNumber,
    setPhoneNumber,
    registerDeliveryPartner,
    checkDeliveryPartnerExists,
    sendDeliveryOTP,
    resendDeliveryOTP,
    verifyDeliveryOTP,
    logoutDeliveryPartner
  };

  return (
    <DeliveryAuthContext.Provider value={value}>
      {children}
    </DeliveryAuthContext.Provider>
  );
};

// Custom hook to use the delivery auth context
export const useDeliveryAuth = () => {
  const context = useContext(DeliveryAuthContext);

  if (!context) {
    throw new Error('useDeliveryAuth must be used within a DeliveryAuthProvider');
  }

  return context;
};

export default DeliveryAuthContext;
